// content.js - 内容脚本，注入到OKX页面
class OKXBatchImporter {
    constructor() {
        this.isImporting = false;
        this.shouldStop = false;
        this.currentIndex = 0;
        this.successCount = 0;
        this.failureCount = 0;
        this.mnemonics = [];
        this.config = {};
        
        this.log('OKX批量导入助手已加载');
        this.injectHelperScript();
    }
    
    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            info: '#2196F3',
            success: '#4CAF50',
            error: '#F44336',
            warning: '#FF9800'
        };
        console.log(`%c[${timestamp}] [OKX助手] ${message}`, `color: ${colors[type]}; font-weight: bold;`);
    }
    
    // 注入辅助脚本到页面上下文
    injectHelperScript() {
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('injected.js');
        script.onload = () => script.remove();
        (document.head || document.documentElement).appendChild(script);
    }
    
    async startBatchImport(data) {
        if (this.isImporting) {
            this.log('已有导入任务在进行中', 'warning');
            return;
        }
        
        this.isImporting = true;
        this.shouldStop = false;
        this.currentIndex = 0;
        this.successCount = 0;
        this.failureCount = 0;
        this.mnemonics = data.mnemonics;
        this.config = data;
        
        this.log(`开始批量导入 ${this.mnemonics.length} 个助记词`);
        
        try {
            for (let i = 0; i < this.mnemonics.length; i++) {
                if (this.shouldStop) {
                    this.log('用户停止了导入', 'warning');
                    break;
                }
                
                this.currentIndex = i;
                this.sendProgress();
                
                const mnemonic = this.mnemonics[i].trim();
                this.log(`开始导入第 ${i + 1}/${this.mnemonics.length} 个助记词`);
                
                const success = await this.importSingleMnemonic(mnemonic, i);
                
                if (success) {
                    this.successCount++;
                    this.log(`第 ${i + 1} 个助记词导入成功`, 'success');
                } else {
                    this.failureCount++;
                    this.log(`第 ${i + 1} 个助记词导入失败`, 'error');
                }
                
                // 等待间隔时间
                if (i < this.mnemonics.length - 1) {
                    await this.wait(this.config.importDelay);
                }
            }
            
            this.sendComplete();
            this.log(`批量导入完成！成功: ${this.successCount}, 失败: ${this.failureCount}`);
            
        } catch (error) {
            this.log(`批量导入出错: ${error.message}`, 'error');
            this.sendError(error.message);
        } finally {
            this.isImporting = false;
        }
    }
    
    async importSingleMnemonic(mnemonic, index) {
        try {
            // 导航到导入页面
            await this.navigateToImportPage();

            // 等待页面加载
            await this.waitForElement('.mnemonic-words-inputs__container__input');

            // 输入助记词
            const success = await this.inputMnemonic(mnemonic);
            if (!success) {
                throw new Error('助记词输入失败');
            }

            // 点击确认按钮
            await this.clickConfirmButton();

            // 等待导入完成
            await this.waitForImportComplete();

            return true;

        } catch (error) {
            this.log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
            return false;
        }
    }
    
    async navigateToImportPage() {
        const importUrl = 'chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key';
        
        if (window.location.href !== importUrl) {
            window.location.href = importUrl;
            await this.wait(2000);
        }
    }
    
    async inputMnemonic(mnemonic) {
        this.log(`导入助记词: [${mnemonic}]`);

        // 获取第一个输入框
        const firstInput = document.querySelector('.mnemonic-words-inputs__container__input');
        if (!firstInput) {
            throw new Error('未找到助记词输入框');
        }

        // 清空所有输入框
        const allInputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        allInputs.forEach(input => input.value = '');

        // 使用与okx_simple_import.js相同的粘贴模拟方法
        return await this.effectivePasteSimulation(firstInput, mnemonic);
    }
    
    async effectivePasteSimulation(targetElement, text) {
        try {
            targetElement.focus();
            targetElement.click();
            await this.wait(100);

            const pasteEvent = new ClipboardEvent('paste', {
                bubbles: true,
                cancelable: true,
                composed: true
            });

            // 模拟clipboardData - 与okx_simple_import.js完全相同
            Object.defineProperty(pasteEvent, 'clipboardData', {
                value: {
                    getData: (format) => {
                        if (format === 'text/plain' || format === 'text') {
                            return text;
                        }
                        return '';
                    },
                    types: ['text/plain'],
                    items: [{
                        kind: 'string',
                        type: 'text/plain',
                        getAsString: (callback) => callback(text)
                    }]
                },
                writable: false,
                configurable: false
            });

            targetElement.dispatchEvent(pasteEvent);

            // 等待OKX处理
            await this.wait(500);

            return true;

        } catch (error) {
            this.log(`导入助记词失败: ${error.message}`, 'error');
            return false;
        }
    }
    

    
    async clickConfirmButton() {
        await this.wait(500);

        const confirmBtn = await this.waitForElement('#app > div > div._affix_oe51y_42 > div > button');

        if (confirmBtn.disabled) {
            // 获取错误提示信息 - 与okx_simple_import.js相同的选择器
            const msg = document.querySelector("#app > div > div.main-container-wrapper > div > div._connect_1lqck_1 > div > div.okui-tabs-panel-list > div > div > form > div.mnemonic-words-inputs > span");
            if (msg) {
                throw new Error(`导入失败: ${msg.textContent}`);
            }
            throw new Error('确认按钮不可用');
        }

        confirmBtn.click();
        this.log('已点击确认按钮');
    }
    
    async waitForImportComplete() {
        this.log('等待导入完成');

        const startTime = Date.now();
        const timeout = 15000; // 15秒超时

        while (Date.now() - startTime < timeout) {
            // 检查URL是否发生变化（导入成功的标志）
            if (!window.location.href.includes('import-with-seed-phrase-and-private-key')) {
                this.log('检测到页面跳转，导入成功');
                return true;
            }

            // 检查是否有错误提示
            const errorElement = document.querySelector('.error, .toast-error, [class*="error"]');
            if (errorElement && errorElement.textContent.trim()) {
                throw new Error(`导入失败: ${errorElement.textContent.trim()}`);
            }

            await this.wait(500);
        }

        throw new Error('导入超时');
    }
    
    async waitForElement(selector, timeout = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
            await this.wait(100);
        }
        
        throw new Error(`元素未找到: ${selector}`);
    }
    
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    sendProgress() {
        chrome.runtime.sendMessage({
            target: 'popup',
            action: 'importProgress',
            data: {
                currentIndex: this.currentIndex,
                successCount: this.successCount,
                failureCount: this.failureCount
            }
        });
    }
    
    sendComplete() {
        chrome.runtime.sendMessage({
            target: 'popup',
            action: 'importComplete',
            data: {
                successCount: this.successCount,
                failureCount: this.failureCount
            }
        });
    }
    
    sendError(error) {
        chrome.runtime.sendMessage({
            target: 'popup',
            action: 'importError',
            data: { error }
        });
    }
    
    stopImport() {
        this.shouldStop = true;
        this.log('收到停止指令');
    }
}

// 初始化
const importer = new OKXBatchImporter();

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    switch (message.action) {
        case 'ping':
            sendResponse({ pong: true });
            break;
        case 'startBatchImport':
            importer.startBatchImport(message.data);
            sendResponse({ received: true });
            break;
        case 'stopImport':
            importer.stopImport();
            sendResponse({ received: true });
            break;
        default:
            sendResponse({ received: false });
    }

    return true; // 保持消息通道开放
});
