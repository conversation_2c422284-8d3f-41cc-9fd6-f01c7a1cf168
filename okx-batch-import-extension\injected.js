// injected.js - 注入到页面上下文的脚本
(function() {
    'use strict';
    
    // 防止重复注入
    if (window.OKXBatchImportHelper) {
        return;
    }
    
    class OKXBatchImportHelper {
        constructor() {
            this.originalClipboardAPI = null;
            this.setupClipboardInterception();
            console.log('OKX批量导入辅助脚本已注入');
        }
        
        setupClipboardInterception() {
            // 保存原始的剪贴板API
            this.originalClipboardAPI = {
                writeText: navigator.clipboard?.writeText?.bind(navigator.clipboard),
                readText: navigator.clipboard?.readText?.bind(navigator.clipboard)
            };
            
            // 增强剪贴板功能
            if (navigator.clipboard) {
                this.enhanceClipboardAPI();
            }
        }
        
        enhanceClipboardAPI() {
            const self = this;
            
            // 增强writeText方法
            navigator.clipboard.writeText = function(text) {
                console.log('剪贴板写入:', text.substring(0, 50) + '...');
                
                // 调用原始方法
                if (self.originalClipboardAPI.writeText) {
                    return self.originalClipboardAPI.writeText(text);
                }
                
                // 备选方案
                return self.fallbackWriteText(text);
            };
            
            // 增强readText方法
            navigator.clipboard.readText = function() {
                console.log('剪贴板读取请求');
                
                if (self.originalClipboardAPI.readText) {
                    return self.originalClipboardAPI.readText();
                }
                
                return self.fallbackReadText();
            };
        }
        
        fallbackWriteText(text) {
            return new Promise((resolve, reject) => {
                try {
                    // 使用传统的execCommand方法
                    const textarea = document.createElement('textarea');
                    textarea.value = text;
                    textarea.style.position = 'fixed';
                    textarea.style.left = '-9999px';
                    document.body.appendChild(textarea);
                    textarea.select();
                    
                    const success = document.execCommand('copy');
                    document.body.removeChild(textarea);
                    
                    if (success) {
                        resolve();
                    } else {
                        reject(new Error('execCommand copy failed'));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        }
        
        fallbackReadText() {
            return new Promise((resolve, reject) => {
                // 由于安全限制，无法直接读取剪贴板
                // 返回空字符串或提示用户手动粘贴
                resolve('');
            });
        }
        
        // 监听粘贴事件并增强处理
        setupPasteEventListener() {
            document.addEventListener('paste', (event) => {
                console.log('检测到粘贴事件:', event);
                
                // 检查是否是助记词输入框
                const target = event.target;
                if (target && target.classList.contains('mnemonic-words-inputs__container__input')) {
                    this.handleMnemonicPaste(event);
                }
            }, true);
        }
        
        handleMnemonicPaste(event) {
            console.log('处理助记词粘贴事件');
            
            try {
                const clipboardData = event.clipboardData || window.clipboardData;
                const pastedText = clipboardData.getData('text/plain');
                
                if (pastedText && pastedText.includes(' ')) {
                    console.log('检测到多单词助记词，触发自动分割');
                    
                    // 延迟执行，确保OKX的处理逻辑先执行
                    setTimeout(() => {
                        this.triggerAutoSplit(pastedText);
                    }, 100);
                }
            } catch (error) {
                console.error('处理粘贴事件失败:', error);
            }
        }
        
        triggerAutoSplit(text) {
            const words = text.trim().split(/\s+/);
            const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
            
            console.log(`尝试自动分割 ${words.length} 个单词到 ${inputs.length} 个输入框`);
            
            // 检查是否已经自动分割
            let alreadySplit = false;
            for (let i = 1; i < Math.min(3, words.length); i++) {
                if (inputs[i] && inputs[i].value.trim() === words[i]) {
                    alreadySplit = true;
                    break;
                }
            }
            
            if (alreadySplit) {
                console.log('检测到已自动分割，无需手动处理');
                return;
            }
            
            // 手动分割
            console.log('执行手动分割');
            for (let i = 0; i < Math.min(words.length, inputs.length); i++) {
                if (inputs[i]) {
                    inputs[i].value = words[i];
                    
                    // 触发事件
                    inputs[i].dispatchEvent(new Event('input', { bubbles: true }));
                    inputs[i].dispatchEvent(new Event('change', { bubbles: true }));
                }
            }
            
            // 触发最后一个输入框的blur事件
            const lastInput = inputs[Math.min(words.length, inputs.length) - 1];
            if (lastInput) {
                lastInput.focus();
                setTimeout(() => lastInput.blur(), 100);
            }
        }
        
        // 提供给content script调用的方法
        simulateRealPaste(element, text) {
            return new Promise((resolve) => {
                try {
                    // 先写入剪贴板
                    navigator.clipboard.writeText(text).then(() => {
                        // 创建真实的粘贴事件
                        const pasteEvent = new ClipboardEvent('paste', {
                            bubbles: true,
                            cancelable: true,
                            clipboardData: new DataTransfer()
                        });
                        
                        // 设置剪贴板数据
                        pasteEvent.clipboardData.setData('text/plain', text);
                        
                        // 触发事件
                        element.focus();
                        element.dispatchEvent(pasteEvent);
                        
                        resolve(true);
                    }).catch(() => {
                        resolve(false);
                    });
                } catch (error) {
                    console.error('模拟粘贴失败:', error);
                    resolve(false);
                }
            });
        }
    }
    
    // 创建全局实例
    window.OKXBatchImportHelper = new OKXBatchImportHelper();
    
    // 设置粘贴事件监听
    window.OKXBatchImportHelper.setupPasteEventListener();
    
    // 通知content script注入完成
    window.dispatchEvent(new CustomEvent('OKXBatchImportHelperReady'));
    
})();
