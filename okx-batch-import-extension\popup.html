<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 400px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 12px;
            color: #666;
        }
        
        .section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        textarea {
            width: 100%;
            height: 120px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            font-size: 12px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .config-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .config-row label {
            font-size: 13px;
            color: #555;
        }
        
        .config-row input {
            width: 80px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }
        
        .button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .button-primary {
            background: #1890ff;
            color: white;
        }
        
        .button-primary:hover {
            background: #40a9ff;
        }
        
        .button-primary:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .button-secondary {
            background: #f0f0f0;
            color: #333;
            margin-top: 8px;
        }
        
        .button-secondary:hover {
            background: #e6e6e6;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 10px;
            text-align: center;
        }
        
        .status-info {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .status-success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .progress {
            margin-top: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #1890ff;
            transition: width 0.3s;
            width: 0%;
        }
        
        .progress-text {
            font-size: 11px;
            color: #666;
            margin-top: 4px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">OKX 批量导入助手</div>
        <div class="subtitle">快速批量导入助记词到OKX钱包</div>
    </div>
    
    <div class="section">
        <div class="section-title">助记词列表</div>
        <textarea id="mnemonicList" placeholder="请输入助记词，每行一个：&#10;pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans&#10;word1 word2 word3 word4 word5 word6 word7 word8 word9 word10 word11 word12"></textarea>
    </div>
    
    <div class="section">
        <div class="section-title">导入配置</div>
        <div class="config-row">
            <label>导入间隔 (秒):</label>
            <input type="number" id="importDelay" value="3" min="1" max="60">
        </div>
        <div class="config-row">
            <label>重试次数:</label>
            <input type="number" id="retryCount" value="3" min="1" max="10">
        </div>
    </div>
    
    <div class="section">
        <button id="startImport" class="button button-primary">开始批量导入</button>
        <button id="stopImport" class="button button-secondary" style="display: none;">停止导入</button>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div id="progress" class="progress" style="display: none;">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="progressText" class="progress-text">0 / 0</div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
