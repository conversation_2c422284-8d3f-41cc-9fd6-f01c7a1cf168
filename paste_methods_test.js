/**
 * 粘贴模拟方法测试集合
 * 包含8种不同的粘贴模拟方法，用于测试哪种方法能成功触发OKX的自动分割
 */

// 工具函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const log = (msg, type = 'info') => {
    const time = new Date().toLocaleTimeString();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50', 
        error: '#F44336',
        warning: '#FF9800'
    };
    console.log(`%c[${time}] ${msg}`, `color: ${colors[type]}; font-weight: bold;`);
};

// 方法1：execCommand复制粘贴
async function method1_execCommand(targetElement, text) {
    log('测试方法1：execCommand复制粘贴', 'info');
    
    try {
        // 创建隐藏textarea
        const textarea = document.createElement('textarea');
        textarea.style.position = 'fixed';
        textarea.style.left = '-9999px';
        textarea.style.opacity = '0';
        textarea.value = text;
        document.body.appendChild(textarea);
        
        // 复制到剪贴板
        textarea.focus();
        textarea.select();
        const copySuccess = document.execCommand('copy');
        document.body.removeChild(textarea);
        
        log(`复制结果: ${copySuccess}`, copySuccess ? 'success' : 'error');
        
        if (copySuccess) {
            // 粘贴到目标元素
            targetElement.focus();
            targetElement.click();
            await wait(100);
            
            const pasteSuccess = document.execCommand('paste');
            log(`粘贴结果: ${pasteSuccess}`, pasteSuccess ? 'success' : 'error');
            return pasteSuccess;
        }
        return false;
    } catch (error) {
        log(`方法1失败: ${error.message}`, 'error');
        return false;
    }
}

// 方法2：navigator.clipboard API
async function method2_clipboardAPI(targetElement, text) {
    log('测试方法2：现代剪贴板API', 'info');
    
    try {
        // 确保页面有焦点
        window.focus();
        document.body.click();
        await wait(100);
        
        // 写入剪贴板
        await navigator.clipboard.writeText(text);
        log('剪贴板写入成功', 'success');
        
        // 聚焦目标元素
        targetElement.focus();
        targetElement.click();
        await wait(100);
        
        // 模拟Ctrl+V
        const ctrlV = new KeyboardEvent('keydown', {
            key: 'v',
            code: 'KeyV',
            ctrlKey: true,
            bubbles: true,
            cancelable: true
        });
        
        const result = targetElement.dispatchEvent(ctrlV);
        log(`Ctrl+V事件结果: ${result}`, 'info');
        
        return true;
    } catch (error) {
        log(`方法2失败: ${error.message}`, 'error');
        return false;
    }
}

// 方法3：ClipboardEvent模拟
async function method3_clipboardEvent(targetElement, text) {
    log('测试方法3：ClipboardEvent事件模拟', 'info');
    
    try {
        targetElement.focus();
        targetElement.click();
        await wait(100);
        
        const pasteEvent = new ClipboardEvent('paste', {
            bubbles: true,
            cancelable: true,
            composed: true
        });
        
        // 模拟clipboardData
        Object.defineProperty(pasteEvent, 'clipboardData', {
            value: {
                getData: (format) => {
                    log(`OKX请求数据格式: ${format}`, 'info');
                    if (format === 'text/plain' || format === 'text') {
                        return text;
                    }
                    return '';
                },
                types: ['text/plain'],
                items: [{
                    kind: 'string',
                    type: 'text/plain',
                    getAsString: (callback) => callback(text)
                }]
            },
            writable: false,
            configurable: false
        });
        
        const result = targetElement.dispatchEvent(pasteEvent);
        log(`ClipboardEvent结果: ${result}`, result ? 'success' : 'error');
        return result;
    } catch (error) {
        log(`方法3失败: ${error.message}`, 'error');
        return false;
    }
}

// 方法4：DataTransfer对象模拟
async function method4_dataTransfer(targetElement, text) {
    log('测试方法4：DataTransfer对象模拟', 'info');
    
    try {
        targetElement.focus();
        targetElement.click();
        await wait(100);
        
        const dataTransfer = new DataTransfer();
        dataTransfer.setData('text/plain', text);
        
        const pasteEvent = new ClipboardEvent('paste', {
            bubbles: true,
            cancelable: true,
            clipboardData: dataTransfer
        });
        
        const result = targetElement.dispatchEvent(pasteEvent);
        log(`DataTransfer结果: ${result}`, result ? 'success' : 'error');
        return result;
    } catch (error) {
        log(`方法4失败: ${error.message}`, 'error');
        return false;
    }
}

// 方法5：输入事件序列模拟
async function method5_inputSequence(targetElement, text) {
    log('测试方法5：输入事件序列模拟', 'info');
    
    try {
        targetElement.focus();
        targetElement.click();
        await wait(100);
        
        targetElement.value = text;
        
        // 触发事件序列
        const events = [
            { name: 'input', event: new Event('input', { bubbles: true }) },
            { name: 'change', event: new Event('change', { bubbles: true }) },
            { name: 'paste', event: new ClipboardEvent('paste', { bubbles: true }) },
            { name: 'keyup', event: new Event('keyup', { bubbles: true }) },
            { name: 'blur', event: new Event('blur', { bubbles: true }) }
        ];
        
        for (const { name, event } of events) {
            targetElement.dispatchEvent(event);
            log(`触发事件: ${name}`, 'info');
            await wait(50);
        }
        
        return true;
    } catch (error) {
        log(`方法5失败: ${error.message}`, 'error');
        return false;
    }
}

// 方法6：真实剪贴板读取
async function method6_realClipboard(targetElement, text) {
    log('测试方法6：真实剪贴板读取', 'info');
    
    try {
        // 确保页面有焦点
        window.focus();
        document.body.click();
        await wait(100);
        
        // 写入剪贴板
        await navigator.clipboard.writeText(text);
        log('剪贴板写入成功', 'success');
        
        // 读取剪贴板验证
        const clipboardText = await navigator.clipboard.readText();
        log(`剪贴板读取: ${clipboardText.substring(0, 20)}...`, 'success');
        
        targetElement.focus();
        targetElement.click();
        await wait(100);
        
        const pasteEvent = new ClipboardEvent('paste', {
            bubbles: true,
            cancelable: true
        });
        
        Object.defineProperty(pasteEvent, 'clipboardData', {
            value: {
                getData: (format) => {
                    log(`OKX请求剪贴板数据: ${format}`, 'info');
                    if (format === 'text/plain' || format === 'text') {
                        return clipboardText;
                    }
                    return '';
                },
                types: ['text/plain'],
                items: [{
                    kind: 'string',
                    type: 'text/plain',
                    getAsString: (callback) => callback(clipboardText)
                }]
            },
            writable: false,
            configurable: false
        });
        
        const result = targetElement.dispatchEvent(pasteEvent);
        log(`真实剪贴板粘贴结果: ${result}`, result ? 'success' : 'error');
        return result;
    } catch (error) {
        log(`方法6失败: ${error.message}`, 'error');
        return false;
    }
}

// 方法7：逐字符输入模拟
async function method7_characterByCharacter(targetElement, text) {
    log('测试方法7：逐字符输入模拟', 'info');

    try {
        targetElement.focus();
        targetElement.click();
        targetElement.value = '';
        await wait(100);

        for (let i = 0; i < text.length; i++) {
            targetElement.value += text[i];

            const inputEvent = new Event('input', { bubbles: true });
            targetElement.dispatchEvent(inputEvent);

            await wait(10); // 模拟真实打字速度
        }

        targetElement.dispatchEvent(new Event('change', { bubbles: true }));
        targetElement.dispatchEvent(new Event('blur', { bubbles: true }));

        log('逐字符输入完成', 'success');
        return true;
    } catch (error) {
        log(`方法7失败: ${error.message}`, 'error');
        return false;
    }
}

// 方法8：完整的Ctrl+V按键序列
async function method8_fullKeySequence(targetElement, text) {
    log('测试方法8：完整按键序列', 'info');

    try {
        // 先写入剪贴板
        try {
            await navigator.clipboard.writeText(text);
            log('使用现代API写入剪贴板', 'success');
        } catch (e) {
            log('现代API失败，使用execCommand', 'warning');
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.left = '-9999px';
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }

        targetElement.focus();
        targetElement.click();
        await wait(100);

        // 完整的按键序列
        const events = [
            { name: 'Ctrl按下', event: new KeyboardEvent('keydown', { key: 'Control', ctrlKey: true, bubbles: true }) },
            { name: 'V按下', event: new KeyboardEvent('keydown', { key: 'v', ctrlKey: true, bubbles: true }) },
            { name: '粘贴事件', event: new ClipboardEvent('paste', { bubbles: true }) },
            { name: 'V释放', event: new KeyboardEvent('keyup', { key: 'v', bubbles: true }) },
            { name: 'Ctrl释放', event: new KeyboardEvent('keyup', { key: 'Control', bubbles: true }) }
        ];

        for (const { name, event } of events) {
            targetElement.dispatchEvent(event);
            log(`触发: ${name}`, 'info');
            await wait(50);
        }

        return true;
    } catch (error) {
        log(`方法8失败: ${error.message}`, 'error');
        return false;
    }
}

// 检查分割结果的函数
function checkSplitResult(testText) {
    const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
    const words = testText.split(' ');

    let successCount = 0;
    let results = [];

    for (let i = 0; i < Math.min(inputs.length, words.length); i++) {
        const actual = inputs[i] ? inputs[i].value.trim() : '';
        const expected = words[i];
        const isCorrect = actual === expected;

        if (isCorrect) successCount++;

        results.push({
            position: i + 1,
            expected: expected,
            actual: actual,
            correct: isCorrect
        });
    }

    return {
        successCount,
        total: words.length,
        successRate: (successCount / words.length * 100).toFixed(1),
        results
    };
}

// 测试单个方法
async function testSingleMethod(methodNumber, testText = null) {
    const text = testText || "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans";
    const targetElement = document.querySelector('.mnemonic-words-inputs__container__input');

    if (!targetElement) {
        log('❌ 未找到目标输入框', 'error');
        return false;
    }

    const methods = [
        method1_execCommand,
        method2_clipboardAPI,
        method3_clipboardEvent,
        method4_dataTransfer,
        method5_inputSequence,
        method6_realClipboard,
        method7_characterByCharacter,
        method8_fullKeySequence
    ];

    if (methodNumber < 1 || methodNumber > methods.length) {
        log(`❌ 方法编号无效，请选择1-${methods.length}`, 'error');
        return false;
    }

    log(`\n=== 测试方法 ${methodNumber} ===`, 'info');

    // 清空所有输入框
    const allInputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
    allInputs.forEach(input => input.value = '');

    try {
        await methods[methodNumber - 1](targetElement, text);

        // 等待处理
        await wait(1500);

        // 检查结果
        const result = checkSplitResult(text);

        log(`\n=== 方法${methodNumber}测试结果 ===`, 'info');
        log(`成功率: ${result.successCount}/${result.total} (${result.successRate}%)`,
            result.successCount === result.total ? 'success' : 'warning');

        // 显示详细结果
        result.results.slice(0, 5).forEach(r => {
            const status = r.correct ? '✅' : '❌';
            log(`${status} 位置${r.position}: "${r.expected}" -> "${r.actual}"`,
                r.correct ? 'success' : 'error');
        });

        // 检查确认按钮状态
        const confirmBtn = document.querySelector('#app > div > div._affix_oe51y_42 > div > button');
        if (confirmBtn) {
            const isEnabled = !confirmBtn.disabled;
            log(`确认按钮状态: ${isEnabled ? '可用' : '不可用'}`, isEnabled ? 'success' : 'warning');
        }

        if (result.successCount >= 3) {
            log(`🎉 方法${methodNumber} 成功触发自动分割！`, 'success');
            return true;
        }

        return false;

    } catch (error) {
        log(`方法${methodNumber}执行失败: ${error.message}`, 'error');
        return false;
    }
}

// 测试所有方法
async function testAllMethods(testText = null) {
    const text = testText || "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans";

    log('='.repeat(60), 'info');
    log('开始测试所有粘贴模拟方法', 'info');
    log('='.repeat(60), 'info');

    for (let i = 1; i <= 8; i++) {
        const success = await testSingleMethod(i, text);

        if (success) {
            log(`\n🎉 找到有效方法：方法${i}`, 'success');
            break;
        }

        // 方法间等待
        await wait(1000);
    }

    log('\n测试完成', 'info');
}

// 使用说明
log('='.repeat(50), 'info');
log('粘贴模拟方法测试工具已加载', 'success');
log('='.repeat(50), 'info');
log('使用方法:', 'info');
log('1. testSingleMethod(1-8) - 测试单个方法', 'info');
log('2. testAllMethods() - 测试所有方法', 'info');
log('3. 方法列表:', 'info');
log('   1: execCommand复制粘贴', 'info');
log('   2: 现代剪贴板API', 'info');
log('   3: ClipboardEvent模拟', 'info');
log('   4: DataTransfer对象', 'info');
log('   5: 输入事件序列', 'info');
log('   6: 真实剪贴板读取', 'info');
log('   7: 逐字符输入', 'info');
log('   8: 完整按键序列', 'info');
log('='.repeat(50), 'info');


testAllMethods();
