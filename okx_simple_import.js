/**
 * OKX钱包批量导入助记词脚本 - 简化版
 * 使用方法：直接在控制台运行，修改助记词列表后执行
 */

// 助记词列表 - 请在此处添加您的助记词
const mnemonics = [
    "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans",
    // 在此添加更多助记词...
];

// 配置
const config = {
    delay: 3000, // 每次导入间隔（毫秒）
    extensionId: 'mcohilncbfahbmgdjkbpemcciiolgcge'
};

// 工具函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const log = (msg, type = 'info') => {
    const time = new Date().toLocaleTimeString();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50', 
        error: '#F44336',
        warning: '#FF9800'
    };
    console.log(`%c[${time}] ${msg}`, `color: ${colors[type]}; font-weight: bold;`);
};

// 等待元素出现
const waitForElement = (selector, timeout = 5000) => {
    return new Promise((resolve, reject) => {
        const start = Date.now();
        const check = () => {
            const el = document.querySelector(selector);
            if (el) return resolve(el);
            if (Date.now() - start > timeout) return reject(new Error(`元素未找到: ${selector}`));
            setTimeout(check, 100);
        };
        check();
    });
};

// 真实粘贴模拟 - 完全模拟用户行为
const simulatePaste = async (element, value) => {
    log(`开始真实粘贴模拟: ${value.substring(0, 20)}...`, 'info');

    try {
        // 第一步：确保页面和元素获得焦点
        window.focus();
        document.body.click();
        await wait(100);

        element.focus();
        element.click();
        await wait(200);

        // 第二步：清空输入框
        element.value = '';
        element.dispatchEvent(new Event('input', { bubbles: true }));
        await wait(100);

        log('准备写入系统剪贴板...', 'info');

        // 第三步：写入系统剪贴板 - 使用用户手势
        await navigator.clipboard.writeText(value);
        log('助记词已写入系统剪贴板', 'success');

        // 第四步：模拟真实的Ctrl+V按键序列
        log('模拟Ctrl+V按键序列...', 'info');

        // 按下Ctrl键
        const ctrlDown = new KeyboardEvent('keydown', {
            key: 'Control',
            code: 'ControlLeft',
            ctrlKey: true,
            bubbles: true,
            cancelable: true
        });
        element.dispatchEvent(ctrlDown);
        await wait(50);

        // 按下V键
        const vDown = new KeyboardEvent('keydown', {
            key: 'v',
            code: 'KeyV',
            ctrlKey: true,
            bubbles: true,
            cancelable: true
        });
        element.dispatchEvent(vDown);
        await wait(50);

        // 创建真实的粘贴事件 - 从系统剪贴板读取
        const pasteEvent = new ClipboardEvent('paste', {
            bubbles: true,
            cancelable: true
        });

        // 重写clipboardData以返回真实剪贴板内容
        Object.defineProperty(pasteEvent, 'clipboardData', {
            value: {
                getData: function(format) {
                    log(`OKX请求剪贴板数据，格式: ${format}`, 'info');
                    if (format === 'text/plain' || format === 'text') {
                        return value;
                    }
                    return '';
                },
                types: ['text/plain'],
                items: [{
                    kind: 'string',
                    type: 'text/plain',
                    getAsString: function(callback) {
                        callback(value);
                    }
                }]
            },
            writable: false,
            configurable: false
        });

        // 触发粘贴事件
        const pasteResult = element.dispatchEvent(pasteEvent);
        log(`粘贴事件已触发，结果: ${pasteResult}`, 'success');

        // 释放V键
        const vUp = new KeyboardEvent('keyup', {
            key: 'v',
            code: 'KeyV',
            bubbles: true,
            cancelable: true
        });
        element.dispatchEvent(vUp);
        await wait(50);

        // 释放Ctrl键
        const ctrlUp = new KeyboardEvent('keyup', {
            key: 'Control',
            code: 'ControlLeft',
            bubbles: true,
            cancelable: true
        });
        element.dispatchEvent(ctrlUp);

        log('Ctrl+V按键序列完成', 'success');

        // 第五步：等待OKX处理粘贴
        await wait(1000);

        // 检查是否触发了自动分割
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        const words = value.split(' ');
        let autoSplitWorked = true;

        for (let i = 0; i < Math.min(3, words.length); i++) { // 只检查前3个
            if (inputs[i].value.trim() !== words[i]) {
                autoSplitWorked = false;
                break;
            }
        }

        if (autoSplitWorked) {
            log('✅ 自动分割成功触发！', 'success');
        } else {
            log('❌ 自动分割未触发，第一个输入框内容:', 'warning');
            log(`"${inputs[0].value}"`, 'warning');
            throw new Error('自动分割未触发');
        }

    } catch (error) {
        log(`真实粘贴失败: ${error.message}`, 'error');

        // 最后的备选方案：手动设置并尝试触发验证
        log('使用最终备选方案...', 'warning');
        element.value = value;

        // 尝试多种事件组合
        const eventSequence = [
            'focus', 'input', 'paste', 'change', 'keyup', 'blur'
        ];

        for (const eventType of eventSequence) {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            element.dispatchEvent(event);
            await wait(100);
        }
    }
};

// 导入单个助记词
const importMnemonic = async (mnemonic, index) => {
    try {
        log(`开始导入第 ${index + 1}/${mnemonics.length} 个助记词`, 'info');
        
        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);
        
        // 等待页面加载
        const firstInput = await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成', 'success');
        
        // 清空所有输入框
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
        });
        await wait(500);
        
        // 使用按键模拟粘贴助记词到第一个框
        await simulatePaste(firstInput, mnemonic);
        await wait(1500); // 增加等待时间让自动分割完成
        
        // 验证分割结果
        log('验证助记词分割结果...', 'info');
        const words = mnemonic.split(' ');
        const updatedInputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');

        // 检查分割是否成功
        let allCorrect = true;
        for (let i = 0; i < Math.min(updatedInputs.length, words.length); i++) {
            const inputValue = updatedInputs[i].value.trim();
            const expectedWord = words[i];
            if (inputValue !== expectedWord) {
                allCorrect = false;
                log(`第 ${i + 1} 个输入框: 期望 "${expectedWord}", 实际 "${inputValue}"`, 'warning');
            }
        }

        if (allCorrect) {
            log('✅ 助记词分割验证成功', 'success');
        } else {
            log('❌ 分割验证失败，但继续尝试导入', 'warning');
        }
        
        // 等待并检查确认按钮状态
        log('检查确认按钮状态...', 'info');
        const confirmBtn = await waitForElement('#app > div > div._affix_oe51y_42 > div > button');

        // 等待按钮状态更新
        let retryCount = 0;
        const maxRetries = 10;

        while (confirmBtn.disabled && retryCount < maxRetries) {
            log(`确认按钮暂不可用，等待中... (${retryCount + 1}/${maxRetries})`, 'warning');
            await wait(500);

            // 重新触发最后一个输入框的事件
            const lastInput = document.querySelector('.mnemonic-words-inputs__container__input:last-child');
            if (lastInput && lastInput.value.trim()) {
                lastInput.focus();
                lastInput.dispatchEvent(new Event('input', { bubbles: true }));
                lastInput.dispatchEvent(new Event('change', { bubbles: true }));
                lastInput.blur();
            }

            retryCount++;
        }

        if (confirmBtn.disabled) {
            // 尝试点击页面其他地方触发验证
            document.body.click();
            await wait(500);

            if (confirmBtn.disabled) {
                throw new Error('确认按钮仍然不可用，可能助记词验证失败');
            }
        }

        log('确认按钮已可用', 'success');
        confirmBtn.click();
        log('已点击确认按钮', 'success');
        
        // 等待导入完成（检查URL变化）
        const startTime = Date.now();
        while (Date.now() - startTime < 15000) {
            if (!window.location.href.includes('import-with-seed-phrase-and-private-key')) {
                log(`第 ${index + 1} 个助记词导入成功`, 'success');
                return true;
            }
            await wait(500);
        }
        
        throw new Error('导入超时');
        
    } catch (error) {
        log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
        return false;
    }
};

// 批量导入主函数
const batchImport = async () => {
    if (mnemonics.length === 0) {
        log('助记词列表为空，请先添加助记词', 'error');
        return;
    }
    
    log('='.repeat(50), 'info');
    log(`开始批量导入 ${mnemonics.length} 个助记词`, 'info');
    log('='.repeat(50), 'info');
    
    let successCount = 0;
    let failureCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < mnemonics.length; i++) {
        const mnemonic = mnemonics[i].trim();
        if (!mnemonic) continue;
        
        const result = await importMnemonic(mnemonic, i);
        if (result) {
            successCount++;
        } else {
            failureCount++;
        }
        
        // 等待间隔
        if (i < mnemonics.length - 1) {
            log(`等待 ${config.delay}ms 后继续...`, 'info');
            await wait(config.delay);
        }
    }
    
    // 统计结果
    const totalTime = Math.round((Date.now() - startTime) / 1000);
    log('='.repeat(50), 'info');
    log('批量导入完成！', 'success');
    log(`总耗时: ${totalTime} 秒`, 'info');
    log(`成功: ${successCount} 个`, 'success');
    log(`失败: ${failureCount} 个`, 'error');
    log('='.repeat(50), 'info');
};


// 专门测试粘贴功能
const testPasteOnly = async () => {
    try {
        log('=== 开始粘贴功能测试 ===', 'info');

        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);

        // 等待页面加载
        const firstInput = await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成，开始粘贴测试', 'success');

        // 测试助记词
        const testMnemonic = mnemonics[0];
        log(`测试助记词: ${testMnemonic}`, 'info');

        // 执行粘贴
        await simulatePaste(firstInput, testMnemonic);

        // 等待处理
        await wait(2000);

        // 检查结果
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        const words = testMnemonic.split(' ');

        log('=== 粘贴测试结果 ===', 'info');
        let successCount = 0;
        for (let i = 0; i < Math.min(inputs.length, words.length); i++) {
            const actual = inputs[i].value.trim();
            const expected = words[i];
            const isCorrect = actual === expected;
            if (isCorrect) successCount++;

            const status = isCorrect ? '✅' : '❌';
            log(`${status} 位置${i+1}: "${expected}" -> "${actual}"`, isCorrect ? 'success' : 'error');
        }

        const successRate = (successCount / words.length * 100).toFixed(1);
        log(`成功率: ${successCount}/${words.length} (${successRate}%)`, successCount === words.length ? 'success' : 'warning');

        // 检查确认按钮状态
        const confirmBtn = document.querySelector('#app > div > div._affix_oe51y_42 > div > button');
        if (confirmBtn) {
            const isEnabled = !confirmBtn.disabled;
            log(`确认按钮状态: ${isEnabled ? '可用' : '不可用'}`, isEnabled ? 'success' : 'error');
        }

    } catch (error) {
        log(`粘贴测试失败: ${error.message}`, 'error');
    }
};

log('='.repeat(50), 'info');
log('OKX钱包批量导入脚本已启动 (真实粘贴版)', 'success');
log('='.repeat(50), 'info');
log('可用命令:', 'info');
log('- batchImport()     开始批量导入', 'info');
log('- testPasteOnly()   仅测试粘贴功能', 'info');
log('='.repeat(50), 'info');

// 自动开始测试
testPasteOnly();
