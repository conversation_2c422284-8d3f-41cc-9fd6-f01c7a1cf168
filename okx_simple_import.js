/**
 * OKX钱包批量导入助记词脚本 - 简化版
 * 使用方法：直接在控制台运行，修改助记词列表后执行
 */

// 助记词列表 - 请在此处添加您的助记词
const mnemonics = [
    "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans",
    // 在此添加更多助记词...
];

// 配置
const config = {
    delay: 3000, // 每次导入间隔（毫秒）
    extensionId: 'mcohilncbfahbmgdjkbpemcciiolgcge'
};

// 工具函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const log = (msg, type = 'info') => {
    const time = new Date().toLocaleTimeString();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50', 
        error: '#F44336',
        warning: '#FF9800'
    };
    console.log(`%c[${time}] ${msg}`, `color: ${colors[type]}; font-weight: bold;`);
};

// 等待元素出现
const waitForElement = (selector, timeout = 5000) => {
    return new Promise((resolve, reject) => {
        const start = Date.now();
        const check = () => {
            const el = document.querySelector(selector);
            if (el) return resolve(el);
            if (Date.now() - start > timeout) return reject(new Error(`元素未找到: ${selector}`));
            setTimeout(check, 100);
        };
        check();
    });
};

// 高级粘贴模拟 - 使用execCommand和隐藏textarea
const advancedPasteSimulation = async (targetElement, value) => {
    log(`开始高级粘贴模拟: ${value.substring(0, 20)}...`, 'info');

    try {
        // 方法1: 使用隐藏textarea作为中介
        log('尝试方法1: 隐藏textarea中介粘贴', 'info');

        // 创建隐藏的textarea
        const hiddenTextarea = document.createElement('textarea');
        hiddenTextarea.style.position = 'fixed';
        hiddenTextarea.style.left = '-9999px';
        hiddenTextarea.style.top = '-9999px';
        hiddenTextarea.style.opacity = '0';
        hiddenTextarea.value = value;
        document.body.appendChild(hiddenTextarea);

        // 选中隐藏textarea的内容
        hiddenTextarea.focus();
        hiddenTextarea.select();

        // 复制到剪贴板
        const copySuccess = document.execCommand('copy');
        log(`复制到剪贴板: ${copySuccess ? '成功' : '失败'}`, copySuccess ? 'success' : 'warning');

        // 清理隐藏元素
        document.body.removeChild(hiddenTextarea);

        if (copySuccess) {
            // 聚焦到目标元素
            targetElement.focus();
            targetElement.click();
            await wait(200);

            // 清空目标元素
            targetElement.value = '';
            targetElement.dispatchEvent(new Event('input', { bubbles: true }));
            await wait(100);

            // 尝试execCommand粘贴
            log('尝试execCommand粘贴...', 'info');
            const pasteSuccess = document.execCommand('paste');
            log(`execCommand粘贴: ${pasteSuccess ? '成功' : '失败'}`, pasteSuccess ? 'success' : 'warning');

            if (pasteSuccess) {
                await wait(1000);

                // 检查是否成功
                const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
                const words = value.split(' ');

                // 检查前3个单词是否正确分割
                let autoSplitWorked = true;
                for (let i = 0; i < Math.min(3, words.length); i++) {
                    if (inputs[i].value.trim() !== words[i]) {
                        autoSplitWorked = false;
                        break;
                    }
                }

                if (autoSplitWorked) {
                    log('✅ execCommand粘贴成功触发自动分割！', 'success');
                    return true;
                }
            }
        }

        // 方法2: 模拟用户选择和粘贴
        log('尝试方法2: 模拟用户选择粘贴', 'info');

        // 先将内容写入剪贴板（需要用户手势）
        try {
            await navigator.clipboard.writeText(value);
            log('成功写入剪贴板', 'success');

            // 聚焦并选择目标元素
            targetElement.focus();
            targetElement.select();
            await wait(100);

            // 创建更真实的粘贴事件
            const pasteEvent = new ClipboardEvent('paste', {
                bubbles: true,
                cancelable: true,
                composed: true
            });

            // 使用真实的剪贴板数据
            const clipboardText = await navigator.clipboard.readText();

            // 重新定义clipboardData
            Object.defineProperty(pasteEvent, 'clipboardData', {
                value: {
                    getData: function(format) {
                        log(`OKX请求剪贴板数据: ${format}`, 'info');
                        if (format === 'text/plain' || format === 'text') {
                            return clipboardText;
                        }
                        return '';
                    },
                    types: ['text/plain'],
                    items: [{
                        kind: 'string',
                        type: 'text/plain',
                        getAsString: function(callback) {
                            callback(clipboardText);
                        }
                    }]
                },
                writable: false,
                configurable: false
            });

            // 触发粘贴事件
            const eventResult = targetElement.dispatchEvent(pasteEvent);
            log(`粘贴事件触发结果: ${eventResult}`, 'info');

            await wait(1000);

            // 检查结果
            const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
            const words = value.split(' ');

            let autoSplitWorked = true;
            for (let i = 0; i < Math.min(3, words.length); i++) {
                if (inputs[i].value.trim() !== words[i]) {
                    autoSplitWorked = false;
                    break;
                }
            }

            if (autoSplitWorked) {
                log('✅ 真实剪贴板粘贴成功！', 'success');
                return true;
            }

        } catch (clipboardError) {
            log(`剪贴板操作失败: ${clipboardError.message}`, 'warning');
        }

        // 方法3: 直接设置值并模拟粘贴事件
        log('尝试方法3: 直接设置值模拟粘贴', 'info');

        targetElement.focus();
        targetElement.value = value;

        // 触发一系列事件
        const events = [
            new Event('input', { bubbles: true }),
            new Event('change', { bubbles: true }),
            new ClipboardEvent('paste', { bubbles: true }),
            new Event('keyup', { bubbles: true })
        ];

        for (const event of events) {
            targetElement.dispatchEvent(event);
            await wait(100);
        }

        await wait(500);

        // 最后检查
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        if (inputs[0].value.includes(' ')) {
            log('检测到完整助记词在第一个输入框中', 'warning');
            return false;
        }

        log('所有粘贴方法尝试完成', 'info');
        return false;

    } catch (error) {
        log(`高级粘贴模拟失败: ${error.message}`, 'error');
        return false;
    }
};

// 备选：直接输入方法
const directInputFallback = async (value) => {
    log('使用备选方案：直接输入模式', 'warning');

    try {
        const words = value.split(' ');
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');

        // 清空所有输入框
        for (let i = 0; i < inputs.length; i++) {
            inputs[i].value = '';
        }

        // 逐个设置每个单词
        for (let i = 0; i < Math.min(words.length, inputs.length); i++) {
            const input = inputs[i];
            const word = words[i];

            input.focus();
            input.value = word;
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            await wait(100);
        }

        // 触发验证
        await triggerValidation();
        return true;

    } catch (error) {
        log(`直接输入备选方案失败: ${error.message}`, 'error');
        return false;
    }
};

// 尝试触发OKX验证的函数
const triggerValidation = async () => {
    log('尝试触发OKX验证逻辑...', 'info');

    try {
        // 获取所有输入框
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');

        // 方法1：触发每个输入框的事件
        for (let i = 0; i < inputs.length; i++) {
            const input = inputs[i];
            if (input.value.trim()) {
                input.focus();
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));
                await wait(50);
            }
        }

        // 方法2：点击页面其他地方
        document.body.click();
        await wait(200);

        // 方法3：触发表单验证
        const form = document.querySelector('form');
        if (form) {
            form.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // 方法4：模拟Tab键切换
        const lastInput = inputs[inputs.length - 1];
        if (lastInput) {
            lastInput.focus();
            const tabEvent = new KeyboardEvent('keydown', {
                key: 'Tab',
                code: 'Tab',
                bubbles: true
            });
            lastInput.dispatchEvent(tabEvent);
        }

        await wait(500);
        log('验证触发完成', 'success');

    } catch (error) {
        log(`触发验证失败: ${error.message}`, 'error');
    }
};

// 导入单个助记词
const importMnemonic = async (mnemonic, index) => {
    try {
        log(`开始导入第 ${index + 1}/${mnemonics.length} 个助记词`, 'info');
        
        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);
        
        // 等待页面加载
        await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成', 'success');
        
        // 清空所有输入框
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
        });
        await wait(500);
        
        // 首先尝试高级粘贴模拟
        const firstInput = document.querySelector('.mnemonic-words-inputs__container__input');
        const pasteSuccess = await advancedPasteSimulation(firstInput, mnemonic);

        if (!pasteSuccess) {
            log('粘贴模拟失败，使用直接输入备选方案', 'warning');
            const inputSuccess = await directInputFallback(mnemonic);
            if (!inputSuccess) {
                throw new Error('所有输入方法都失败');
            }
        }

        await wait(1000);
        
        // 验证分割结果
        log('验证助记词分割结果...', 'info');
        const words = mnemonic.split(' ');
        const updatedInputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');

        // 检查分割是否成功
        let allCorrect = true;
        for (let i = 0; i < Math.min(updatedInputs.length, words.length); i++) {
            const inputValue = updatedInputs[i].value.trim();
            const expectedWord = words[i];
            if (inputValue !== expectedWord) {
                allCorrect = false;
                log(`第 ${i + 1} 个输入框: 期望 "${expectedWord}", 实际 "${inputValue}"`, 'warning');
            }
        }

        if (allCorrect) {
            log('✅ 助记词分割验证成功', 'success');
        } else {
            log('❌ 分割验证失败，但继续尝试导入', 'warning');
        }
        
        // 等待并检查确认按钮状态
        log('检查确认按钮状态...', 'info');
        const confirmBtn = await waitForElement('#app > div > div._affix_oe51y_42 > div > button');

        // 等待按钮状态更新
        let retryCount = 0;
        const maxRetries = 10;

        while (confirmBtn.disabled && retryCount < maxRetries) {
            log(`确认按钮暂不可用，等待中... (${retryCount + 1}/${maxRetries})`, 'warning');
            await wait(500);

            // 重新触发最后一个输入框的事件
            const lastInput = document.querySelector('.mnemonic-words-inputs__container__input:last-child');
            if (lastInput && lastInput.value.trim()) {
                lastInput.focus();
                lastInput.dispatchEvent(new Event('input', { bubbles: true }));
                lastInput.dispatchEvent(new Event('change', { bubbles: true }));
                lastInput.blur();
            }

            retryCount++;
        }

        if (confirmBtn.disabled) {
            // 尝试点击页面其他地方触发验证
            document.body.click();
            await wait(500);

            if (confirmBtn.disabled) {
                throw new Error('确认按钮仍然不可用，可能助记词验证失败');
            }
        }

        log('确认按钮已可用', 'success');
        confirmBtn.click();
        log('已点击确认按钮', 'success');
        
        // 等待导入完成（检查URL变化）
        const startTime = Date.now();
        while (Date.now() - startTime < 15000) {
            if (!window.location.href.includes('import-with-seed-phrase-and-private-key')) {
                log(`第 ${index + 1} 个助记词导入成功`, 'success');
                return true;
            }
            await wait(500);
        }
        
        throw new Error('导入超时');
        
    } catch (error) {
        log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
        return false;
    }
};

// 批量导入主函数
const batchImport = async () => {
    if (mnemonics.length === 0) {
        log('助记词列表为空，请先添加助记词', 'error');
        return;
    }
    
    log('='.repeat(50), 'info');
    log(`开始批量导入 ${mnemonics.length} 个助记词`, 'info');
    log('='.repeat(50), 'info');
    
    let successCount = 0;
    let failureCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < mnemonics.length; i++) {
        const mnemonic = mnemonics[i].trim();
        if (!mnemonic) continue;
        
        const result = await importMnemonic(mnemonic, i);
        if (result) {
            successCount++;
        } else {
            failureCount++;
        }
        
        // 等待间隔
        if (i < mnemonics.length - 1) {
            log(`等待 ${config.delay}ms 后继续...`, 'info');
            await wait(config.delay);
        }
    }
    
    // 统计结果
    const totalTime = Math.round((Date.now() - startTime) / 1000);
    log('='.repeat(50), 'info');
    log('批量导入完成！', 'success');
    log(`总耗时: ${totalTime} 秒`, 'info');
    log(`成功: ${successCount} 个`, 'success');
    log(`失败: ${failureCount} 个`, 'error');
    log('='.repeat(50), 'info');
};


// 测试高级粘贴功能
const testAdvancedPaste = async () => {
    try {
        log('=== 开始高级粘贴功能测试 ===', 'info');

        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);

        // 等待页面加载
        await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成，开始高级粘贴测试', 'success');

        // 测试助记词
        const testMnemonic = mnemonics[0];
        log(`测试助记词: ${testMnemonic}`, 'info');

        // 获取第一个输入框
        const firstInput = document.querySelector('.mnemonic-words-inputs__container__input');

        // 执行高级粘贴模拟
        const pasteSuccess = await advancedPasteSimulation(firstInput, testMnemonic);

        if (!pasteSuccess) {
            log('高级粘贴失败，尝试备选方案...', 'warning');
            await directInputFallback(testMnemonic);
        }

        await wait(2000);

        // 检查结果
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        const words = testMnemonic.split(' ');

        log('=== 高级粘贴测试结果 ===', 'info');
        let successCount = 0;
        for (let i = 0; i < Math.min(inputs.length, words.length); i++) {
            const actual = inputs[i].value.trim();
            const expected = words[i];
            const isCorrect = actual === expected;
            if (isCorrect) successCount++;

            const status = isCorrect ? '✅' : '❌';
            log(`${status} 位置${i+1}: "${expected}" -> "${actual}"`, isCorrect ? 'success' : 'error');
        }

        const successRate = (successCount / words.length * 100).toFixed(1);
        log(`成功率: ${successCount}/${words.length} (${successRate}%)`, successCount === words.length ? 'success' : 'warning');

        // 检查确认按钮状态
        const confirmBtn = document.querySelector('#app > div > div._affix_oe51y_42 > div > button');
        if (confirmBtn) {
            const isEnabled = !confirmBtn.disabled;
            log(`确认按钮状态: ${isEnabled ? '可用' : '不可用'}`, isEnabled ? 'success' : 'error');

            if (isEnabled) {
                log('🎉 测试成功！确认按钮已激活', 'success');
            } else {
                log('⚠️ 确认按钮仍未激活，可能需要额外验证', 'warning');
            }
        }

    } catch (error) {
        log(`高级粘贴测试失败: ${error.message}`, 'error');
    }
};

log('='.repeat(50), 'info');
log('OKX钱包批量导入脚本已启动 (高级粘贴版)', 'success');
log('='.repeat(50), 'info');
log('可用命令:', 'info');
log('- batchImport()         开始批量导入', 'info');
log('- testAdvancedPaste()   测试高级粘贴功能', 'info');
log('='.repeat(50), 'info');
log('📋 高级粘贴模拟包含以下方法:', 'info');
log('1. execCommand复制粘贴 (兼容性最好)', 'info');
log('2. 真实剪贴板API粘贴 (现代浏览器)', 'info');
log('3. 直接输入备选方案 (保底方案)', 'info');
log('='.repeat(50), 'info');

// 自动开始测试
testAdvancedPaste();
