// 助记词列表 - 请在此处添加您的助记词
const mnemonics = [

];

// 配置
const config = {
    delay: 1500, // 每次导入间隔（毫秒）
    extensionId: 'mcohilncbfahbmgdjkbpemcciiolgcge'
};

// 工具函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const log = (msg, type = 'info') => {
    const time = new Date().toLocaleTimeString();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50', 
        error: '#F44336',
        warning: '#FF9800'
    };
    console.log(`%c[${time}] ${msg}`, `color: ${colors[type]}; font-weight: bold;`);
};

// 等待元素出现
const waitForElement = (selector, timeout = 5000) => {
    return new Promise((resolve, reject) => {
        const start = Date.now();
        const check = () => {
            const el = document.querySelector(selector);
            if (el) return resolve(el);
            if (Date.now() - start > timeout) return reject(new Error(`元素未找到: ${selector}`));
            setTimeout(check, 100);
        };
        check();
    });
};

// 有效的粘贴模拟 - 使用ClipboardEvent
const effectivePasteSimulation = async (targetElement, text) => {
    log(`导入助记词: [${text}]`, 'info');

    try {
        targetElement.focus();
        targetElement.click();
        await wait(100);

        const pasteEvent = new ClipboardEvent('paste', {
            bubbles: true,
            cancelable: true,
            composed: true
        });

        // 模拟clipboardData
        Object.defineProperty(pasteEvent, 'clipboardData', {
            value: {
                getData: (format) => {
                    if (format === 'text/plain' || format === 'text') {
                        return text;
                    }
                    return '';
                },
                types: ['text/plain'],
                items: [{
                    kind: 'string',
                    type: 'text/plain',
                    getAsString: (callback) => callback(text)
                }]
            },
            writable: false,
            configurable: false
        });

        const result = targetElement.dispatchEvent(pasteEvent);

        // 等待OKX处理
        await wait(500);
        
        return true;

    } catch (error) {
        log(`导入助记词失败: ${error.message}`, 'error');
        return false;
    }
};


// 导入单个助记词
const importMnemonic = async (mnemonic, index) => {
    try {
        log(`开始导入第 ${index + 1}/${mnemonics.length} 个助记词`, 'info');
        
        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(500);
        
        // 等待页面加载
        await waitForElement('.mnemonic-words-inputs__container__input');
        
        // 使用有效的粘贴模拟
        const firstInput = document.querySelector('.mnemonic-words-inputs__container__input');
        const pasteSuccess = await effectivePasteSimulation(firstInput, mnemonic);

        await wait(500);
        
        const confirmBtn = await waitForElement('#app > div > div._affix_oe51y_42 > div > button');

        if (confirmBtn.disabled) {
            const msg = document.querySelector("#app > div > div.main-container-wrapper > div > div._connect_1lqck_1 > div > div.okui-tabs-panel-list > div > div > form > div.mnemonic-words-inputs > span");
            if (msg) {
                throw new Error(`导入失败: ${msg.textContent}`);
            }
        }

        confirmBtn.click();
        
        // 等待导入完成（检查URL变化）
        const startTime = Date.now();
        while (Date.now() - startTime < 2000) {
            if (!window.location.href.includes('import-with-seed-phrase-and-private-key')) {
                log(`第 ${index + 1} 个助记词导入成功`, 'success');
                return true;
            }
            await wait(500);
        }

        throw new Error('导入超时');
        
    } catch (error) {
        log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
        return false;
    }
};

// 批量导入主函数
const batchImport = async () => {
    if (mnemonics.length === 0) {
        log('助记词列表为空，请先添加助记词', 'error');
        return;
    }
    
    log('='.repeat(50), 'info');
    log(`开始批量导入 ${mnemonics.length} 个助记词`, 'info');
    log('='.repeat(50), 'info');
    
    let successCount = 0;
    let failureCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < mnemonics.length; i++) {
        const mnemonic = mnemonics[i].trim();
        if (!mnemonic) continue;
        
        const result = await importMnemonic(mnemonic, i);
        if (result) {
            successCount++;
        } else {
            failureCount++;
        }
        
        // 等待间隔
        if (i < mnemonics.length - 1) {
            await wait(config.delay);
        }
    }
    
    // 统计结果
    const totalTime = Math.round((Date.now() - startTime) / 1000);
    log('='.repeat(50), 'info');
    log('批量导入完成！', 'success');
    log(`总耗时: ${totalTime} 秒`, 'info');
    log(`成功: ${successCount} 个`, 'success');
    log(`失败: ${failureCount} 个`, 'error');
    log('='.repeat(50), 'info');
};

log('='.repeat(50), 'info');
log('OKX钱包批量导入脚本已启动', 'success');

batchImport();