/**
 * OKX钱包批量导入助记词脚本 - 简化版
 * 使用方法：直接在控制台运行，修改助记词列表后执行
 */

// 助记词列表 - 请在此处添加您的助记词
const mnemonics = [
    "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans",
    // 在此添加更多助记词...
];

// 配置
const config = {
    delay: 3000, // 每次导入间隔（毫秒）
    extensionId: 'mcohilncbfahbmgdjkbpemcciiolgcge'
};

// 工具函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const log = (msg, type = 'info') => {
    const time = new Date().toLocaleTimeString();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50', 
        error: '#F44336',
        warning: '#FF9800'
    };
    console.log(`%c[${time}] ${msg}`, `color: ${colors[type]}; font-weight: bold;`);
};

// 等待元素出现
const waitForElement = (selector, timeout = 5000) => {
    return new Promise((resolve, reject) => {
        const start = Date.now();
        const check = () => {
            const el = document.querySelector(selector);
            if (el) return resolve(el);
            if (Date.now() - start > timeout) return reject(new Error(`元素未找到: ${selector}`));
            setTimeout(check, 100);
        };
        check();
    });
};

// 直接DOM操作 - 绕过粘贴事件
const directInput = async (value) => {
    log(`开始直接输入模式: ${value.substring(0, 20)}...`, 'info');

    try {
        const words = value.split(' ');
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');

        if (inputs.length < words.length) {
            throw new Error(`输入框数量不足: 需要${words.length}个，找到${inputs.length}个`);
        }

        log(`开始逐个输入 ${words.length} 个单词...`, 'info');

        // 清空所有输入框
        for (let i = 0; i < inputs.length; i++) {
            inputs[i].value = '';
        }

        // 逐个输入每个单词
        for (let i = 0; i < words.length; i++) {
            const input = inputs[i];
            const word = words[i];

            log(`输入第 ${i + 1} 个单词: ${word}`, 'info');

            // 聚焦到输入框
            input.focus();
            input.click();
            await wait(100);

            // 清空并设置值
            input.value = '';
            await wait(50);

            // 模拟逐字符输入
            for (let j = 0; j < word.length; j++) {
                input.value += word[j];

                // 触发输入事件
                const inputEvent = new Event('input', {
                    bubbles: true,
                    cancelable: true
                });
                input.dispatchEvent(inputEvent);

                await wait(20); // 模拟真实打字速度
            }

            // 触发完成事件
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.dispatchEvent(new Event('blur', { bubbles: true }));

            await wait(100);
        }

        log('所有单词输入完成', 'success');

        // 触发最后一个输入框的焦点事件，确保验证
        const lastInput = inputs[words.length - 1];
        lastInput.focus();
        await wait(200);
        lastInput.blur();
        await wait(500);

        // 验证输入结果
        let allCorrect = true;
        for (let i = 0; i < words.length; i++) {
            if (inputs[i].value.trim() !== words[i]) {
                allCorrect = false;
                log(`第 ${i + 1} 个单词验证失败: 期望"${words[i]}", 实际"${inputs[i].value.trim()}"`, 'error');
            }
        }

        if (allCorrect) {
            log('✅ 所有单词输入验证成功', 'success');
        } else {
            throw new Error('单词输入验证失败');
        }

        return true;

    } catch (error) {
        log(`直接输入失败: ${error.message}`, 'error');
        return false;
    }
};

// 尝试触发OKX验证的函数
const triggerValidation = async () => {
    log('尝试触发OKX验证逻辑...', 'info');

    try {
        // 获取所有输入框
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');

        // 方法1：触发每个输入框的事件
        for (let i = 0; i < inputs.length; i++) {
            const input = inputs[i];
            if (input.value.trim()) {
                input.focus();
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));
                await wait(50);
            }
        }

        // 方法2：点击页面其他地方
        document.body.click();
        await wait(200);

        // 方法3：触发表单验证
        const form = document.querySelector('form');
        if (form) {
            form.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // 方法4：模拟Tab键切换
        const lastInput = inputs[inputs.length - 1];
        if (lastInput) {
            lastInput.focus();
            const tabEvent = new KeyboardEvent('keydown', {
                key: 'Tab',
                code: 'Tab',
                bubbles: true
            });
            lastInput.dispatchEvent(tabEvent);
        }

        await wait(500);
        log('验证触发完成', 'success');

    } catch (error) {
        log(`触发验证失败: ${error.message}`, 'error');
    }
};

// 导入单个助记词
const importMnemonic = async (mnemonic, index) => {
    try {
        log(`开始导入第 ${index + 1}/${mnemonics.length} 个助记词`, 'info');
        
        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);
        
        // 等待页面加载
        await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成', 'success');
        
        // 清空所有输入框
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
        });
        await wait(500);
        
        // 使用直接输入模式
        const inputSuccess = await directInput(mnemonic);
        if (!inputSuccess) {
            throw new Error('助记词输入失败');
        }

        // 触发验证逻辑
        await triggerValidation();
        await wait(1000);
        
        // 验证分割结果
        log('验证助记词分割结果...', 'info');
        const words = mnemonic.split(' ');
        const updatedInputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');

        // 检查分割是否成功
        let allCorrect = true;
        for (let i = 0; i < Math.min(updatedInputs.length, words.length); i++) {
            const inputValue = updatedInputs[i].value.trim();
            const expectedWord = words[i];
            if (inputValue !== expectedWord) {
                allCorrect = false;
                log(`第 ${i + 1} 个输入框: 期望 "${expectedWord}", 实际 "${inputValue}"`, 'warning');
            }
        }

        if (allCorrect) {
            log('✅ 助记词分割验证成功', 'success');
        } else {
            log('❌ 分割验证失败，但继续尝试导入', 'warning');
        }
        
        // 等待并检查确认按钮状态
        log('检查确认按钮状态...', 'info');
        const confirmBtn = await waitForElement('#app > div > div._affix_oe51y_42 > div > button');

        // 等待按钮状态更新
        let retryCount = 0;
        const maxRetries = 10;

        while (confirmBtn.disabled && retryCount < maxRetries) {
            log(`确认按钮暂不可用，等待中... (${retryCount + 1}/${maxRetries})`, 'warning');
            await wait(500);

            // 重新触发最后一个输入框的事件
            const lastInput = document.querySelector('.mnemonic-words-inputs__container__input:last-child');
            if (lastInput && lastInput.value.trim()) {
                lastInput.focus();
                lastInput.dispatchEvent(new Event('input', { bubbles: true }));
                lastInput.dispatchEvent(new Event('change', { bubbles: true }));
                lastInput.blur();
            }

            retryCount++;
        }

        if (confirmBtn.disabled) {
            // 尝试点击页面其他地方触发验证
            document.body.click();
            await wait(500);

            if (confirmBtn.disabled) {
                throw new Error('确认按钮仍然不可用，可能助记词验证失败');
            }
        }

        log('确认按钮已可用', 'success');
        confirmBtn.click();
        log('已点击确认按钮', 'success');
        
        // 等待导入完成（检查URL变化）
        const startTime = Date.now();
        while (Date.now() - startTime < 15000) {
            if (!window.location.href.includes('import-with-seed-phrase-and-private-key')) {
                log(`第 ${index + 1} 个助记词导入成功`, 'success');
                return true;
            }
            await wait(500);
        }
        
        throw new Error('导入超时');
        
    } catch (error) {
        log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
        return false;
    }
};

// 批量导入主函数
const batchImport = async () => {
    if (mnemonics.length === 0) {
        log('助记词列表为空，请先添加助记词', 'error');
        return;
    }
    
    log('='.repeat(50), 'info');
    log(`开始批量导入 ${mnemonics.length} 个助记词`, 'info');
    log('='.repeat(50), 'info');
    
    let successCount = 0;
    let failureCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < mnemonics.length; i++) {
        const mnemonic = mnemonics[i].trim();
        if (!mnemonic) continue;
        
        const result = await importMnemonic(mnemonic, i);
        if (result) {
            successCount++;
        } else {
            failureCount++;
        }
        
        // 等待间隔
        if (i < mnemonics.length - 1) {
            log(`等待 ${config.delay}ms 后继续...`, 'info');
            await wait(config.delay);
        }
    }
    
    // 统计结果
    const totalTime = Math.round((Date.now() - startTime) / 1000);
    log('='.repeat(50), 'info');
    log('批量导入完成！', 'success');
    log(`总耗时: ${totalTime} 秒`, 'info');
    log(`成功: ${successCount} 个`, 'success');
    log(`失败: ${failureCount} 个`, 'error');
    log('='.repeat(50), 'info');
};


// 专门测试直接输入功能
const testDirectInput = async () => {
    try {
        log('=== 开始直接输入功能测试 ===', 'info');

        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);

        // 等待页面加载
        await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成，开始直接输入测试', 'success');

        // 测试助记词
        const testMnemonic = mnemonics[0];
        log(`测试助记词: ${testMnemonic}`, 'info');

        // 执行直接输入
        const inputSuccess = await directInput(testMnemonic);

        if (inputSuccess) {
            log('直接输入成功，开始触发验证...', 'success');
            await triggerValidation();
            await wait(2000);
        }

        // 检查结果
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        const words = testMnemonic.split(' ');

        log('=== 直接输入测试结果 ===', 'info');
        let successCount = 0;
        for (let i = 0; i < Math.min(inputs.length, words.length); i++) {
            const actual = inputs[i].value.trim();
            const expected = words[i];
            const isCorrect = actual === expected;
            if (isCorrect) successCount++;

            const status = isCorrect ? '✅' : '❌';
            log(`${status} 位置${i+1}: "${expected}" -> "${actual}"`, isCorrect ? 'success' : 'error');
        }

        const successRate = (successCount / words.length * 100).toFixed(1);
        log(`成功率: ${successCount}/${words.length} (${successRate}%)`, successCount === words.length ? 'success' : 'warning');

        // 检查确认按钮状态
        const confirmBtn = document.querySelector('#app > div > div._affix_oe51y_42 > div > button');
        if (confirmBtn) {
            const isEnabled = !confirmBtn.disabled;
            log(`确认按钮状态: ${isEnabled ? '可用' : '不可用'}`, isEnabled ? 'success' : 'error');

            if (isEnabled) {
                log('🎉 测试成功！确认按钮已激活', 'success');
            } else {
                log('⚠️ 确认按钮仍未激活，可能需要额外验证', 'warning');
            }
        }

    } catch (error) {
        log(`直接输入测试失败: ${error.message}`, 'error');
    }
};

log('='.repeat(50), 'info');
log('OKX钱包批量导入脚本已启动 (真实粘贴版)', 'success');
log('='.repeat(50), 'info');
log('可用命令:', 'info');
log('- batchImport()     开始批量导入', 'info');
log('- testPasteOnly()   仅测试粘贴功能', 'info');
log('='.repeat(50), 'info');

// 自动开始测试
testPasteOnly();
