/**
 * OKX钱包批量导入助记词脚本 - 简化版
 * 使用方法：直接在控制台运行，修改助记词列表后执行
 */

// 助记词列表 - 请在此处添加您的助记词
const mnemonics = [
    "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans",
    // 在此添加更多助记词...
];

// 配置
const config = {
    delay: 3000, // 每次导入间隔（毫秒）
    extensionId: 'mcohilncbfahbmgdjkbpemcciiolgcge'
};

// 工具函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const log = (msg, type = 'info') => {
    const time = new Date().toLocaleTimeString();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50', 
        error: '#F44336',
        warning: '#FF9800'
    };
    console.log(`%c[${time}] ${msg}`, `color: ${colors[type]}; font-weight: bold;`);
};

// 等待元素出现
const waitForElement = (selector, timeout = 5000) => {
    return new Promise((resolve, reject) => {
        const start = Date.now();
        const check = () => {
            const el = document.querySelector(selector);
            if (el) return resolve(el);
            if (Date.now() - start > timeout) return reject(new Error(`元素未找到: ${selector}`));
            setTimeout(check, 100);
        };
        check();
    });
};

// 模拟按键粘贴
const simulatePaste = async (element, value) => {
    try {
        log(`准备将助记词写入剪贴板: ${value.substring(0, 20)}...`, 'info');

        // 将助记词写入剪贴板
        await navigator.clipboard.writeText(value);
        log('助记词已写入剪贴板', 'success');

        // 清空并聚焦到输入框
        element.value = '';
        element.focus();
        log('已聚焦到第一个输入框', 'info');

        await wait(200);

        // 模拟 Ctrl+V 按键
        log('模拟 Ctrl+V 按键...', 'info');

        // 创建 keydown 事件 (Ctrl)
        const ctrlKeyDown = new KeyboardEvent('keydown', {
            key: 'Control',
            code: 'ControlLeft',
            ctrlKey: true,
            bubbles: true,
            cancelable: true
        });

        // 创建 keydown 事件 (V)
        const vKeyDown = new KeyboardEvent('keydown', {
            key: 'v',
            code: 'KeyV',
            ctrlKey: true,
            bubbles: true,
            cancelable: true
        });

        // 创建 keyup 事件
        const ctrlKeyUp = new KeyboardEvent('keyup', {
            key: 'Control',
            code: 'ControlLeft',
            bubbles: true,
            cancelable: true
        });

        const vKeyUp = new KeyboardEvent('keyup', {
            key: 'v',
            code: 'KeyV',
            bubbles: true,
            cancelable: true
        });

        // 按顺序触发事件
        element.dispatchEvent(ctrlKeyDown);
        await wait(50);
        element.dispatchEvent(vKeyDown);
        await wait(50);
        element.dispatchEvent(vKeyUp);
        await wait(50);
        element.dispatchEvent(ctrlKeyUp);

        log('Ctrl+V 按键事件已触发', 'success');

    } catch (error) {
        log(`剪贴板操作失败: ${error.message}`, 'error');

        // 备选方案：直接设置值并触发事件
        log('使用备选方案：直接设置输入框值', 'warning');
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('paste', { bubbles: true }));
    }
};

// 导入单个助记词
const importMnemonic = async (mnemonic, index) => {
    try {
        log(`开始导入第 ${index + 1}/${mnemonics.length} 个助记词`, 'info');
        
        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);
        
        // 等待页面加载
        const firstInput = await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成', 'success');
        
        // 清空所有输入框
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
        });
        await wait(500);
        
        // 使用按键模拟粘贴助记词到第一个框
        await simulatePaste(firstInput, mnemonic);
        await wait(1500); // 增加等待时间让自动分割完成
        
        // 验证自动分割
        log('开始验证助记词自动分割结果...', 'info');
        const words = mnemonic.split(' ');
        log(`期望的助记词: ${words.join(', ')}`, 'info');

        // 重新获取输入框以确保是最新状态
        const updatedInputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        log(`找到 ${updatedInputs.length} 个输入框`, 'info');

        // 显示当前输入框的值
        const currentValues = [];
        for (let i = 0; i < updatedInputs.length; i++) {
            const value = updatedInputs[i].value.trim();
            currentValues.push(value || '(空)');
        }
        log(`当前输入框值: ${currentValues.join(', ')}`, 'info');

        // 检查分割结果
        let success = true;
        let failedIndex = -1;
        for (let i = 0; i < Math.min(updatedInputs.length, words.length); i++) {
            const inputValue = updatedInputs[i].value.trim();
            const expectedWord = words[i];
            if (inputValue !== expectedWord) {
                success = false;
                failedIndex = i;
                log(`第 ${i + 1} 个输入框不匹配: 期望 "${expectedWord}", 实际 "${inputValue}"`, 'error');
                break;
            }
        }

        if (!success) {
            // 尝试手动分割作为备选方案
            log('自动分割失败，尝试手动分割...', 'warning');
            for (let i = 0; i < Math.min(updatedInputs.length, words.length); i++) {
                updatedInputs[i].value = words[i];
                updatedInputs[i].dispatchEvent(new Event('input', { bubbles: true }));
                updatedInputs[i].dispatchEvent(new Event('change', { bubbles: true }));
            }
            await wait(500);

            // 再次验证
            let manualSuccess = true;
            for (let i = 0; i < Math.min(updatedInputs.length, words.length); i++) {
                if (updatedInputs[i].value.trim() !== words[i]) {
                    manualSuccess = false;
                    break;
                }
            }

            if (!manualSuccess) {
                throw new Error('助记词自动分割和手动分割都失败');
            }
            log('手动分割成功', 'success');
        } else {
            log('助记词自动分割成功', 'success');
        }
        
        // 点击确认按钮
        const confirmBtn = await waitForElement('#app > div > div._affix_oe51y_42 > div > button');
        if (confirmBtn.disabled) {
            throw new Error('确认按钮不可用');
        }
        
        confirmBtn.click();
        log('已点击确认按钮', 'success');
        
        // 等待导入完成（检查URL变化）
        const startTime = Date.now();
        while (Date.now() - startTime < 15000) {
            if (!window.location.href.includes('import-with-seed-phrase-and-private-key')) {
                log(`第 ${index + 1} 个助记词导入成功`, 'success');
                return true;
            }
            await wait(500);
        }
        
        throw new Error('导入超时');
        
    } catch (error) {
        log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
        return false;
    }
};

// 批量导入主函数
const batchImport = async () => {
    if (mnemonics.length === 0) {
        log('助记词列表为空，请先添加助记词', 'error');
        return;
    }
    
    log('='.repeat(50), 'info');
    log(`开始批量导入 ${mnemonics.length} 个助记词`, 'info');
    log('='.repeat(50), 'info');
    
    let successCount = 0;
    let failureCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < mnemonics.length; i++) {
        const mnemonic = mnemonics[i].trim();
        if (!mnemonic) continue;
        
        const result = await importMnemonic(mnemonic, i);
        if (result) {
            successCount++;
        } else {
            failureCount++;
        }
        
        // 等待间隔
        if (i < mnemonics.length - 1) {
            log(`等待 ${config.delay}ms 后继续...`, 'info');
            await wait(config.delay);
        }
    }
    
    // 统计结果
    const totalTime = Math.round((Date.now() - startTime) / 1000);
    log('='.repeat(50), 'info');
    log('批量导入完成！', 'success');
    log(`总耗时: ${totalTime} 秒`, 'info');
    log(`成功: ${successCount} 个`, 'success');
    log(`失败: ${failureCount} 个`, 'error');
    log('='.repeat(50), 'info');
};

// 测试函数 - 仅测试粘贴功能
const testPaste = async () => {
    try {
        log('开始测试粘贴功能...', 'info');

        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);

        // 等待页面加载
        const firstInput = await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成，开始测试粘贴', 'success');

        // 测试粘贴
        const testMnemonic = mnemonics[0];
        await simulatePaste(firstInput, testMnemonic);
        await wait(2000);

        // 检查结果
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        const words = testMnemonic.split(' ');

        log('=== 粘贴测试结果 ===', 'info');
        for (let i = 0; i < Math.min(inputs.length, words.length); i++) {
            const actual = inputs[i].value.trim();
            const expected = words[i];
            const status = actual === expected ? '✅' : '❌';
            log(`${status} 第${i+1}个: 期望"${expected}" 实际"${actual}"`, actual === expected ? 'success' : 'error');
        }

    } catch (error) {
        log(`测试失败: ${error.message}`, 'error');
    }
};

log('='.repeat(50), 'info');
log('OKX钱包批量导入脚本已启动 (优化版)', 'success');
log('='.repeat(50), 'info');
log('可用命令:', 'info');
log('- batchImport()  开始批量导入', 'info');
log('- testPaste()    测试粘贴功能', 'info');
log('='.repeat(50), 'info');

// 自动开始批量导入
batchImport();
