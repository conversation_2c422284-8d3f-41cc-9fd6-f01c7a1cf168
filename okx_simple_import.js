/**
 * OKX钱包批量导入助记词脚本 - 简化版
 * 使用方法：直接在控制台运行，修改助记词列表后执行
 */

// 助记词列表 - 请在此处添加您的助记词
const mnemonics = [
    "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans",
    // 在此添加更多助记词...
];

// 配置
const config = {
    delay: 3000, // 每次导入间隔（毫秒）
    extensionId: 'mcohilncbfahbmgdjkbpemcciiolgcge'
};

// 工具函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const log = (msg, type = 'info') => {
    const time = new Date().toLocaleTimeString();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50', 
        error: '#F44336',
        warning: '#FF9800'
    };
    console.log(`%c[${time}] ${msg}`, `color: ${colors[type]}; font-weight: bold;`);
};

// 等待元素出现
const waitForElement = (selector, timeout = 5000) => {
    return new Promise((resolve, reject) => {
        const start = Date.now();
        const check = () => {
            const el = document.querySelector(selector);
            if (el) return resolve(el);
            if (Date.now() - start > timeout) return reject(new Error(`元素未找到: ${selector}`));
            setTimeout(check, 100);
        };
        check();
    });
};

// 改进的粘贴模拟
const simulatePaste = async (element, value) => {
    try {
        log(`开始模拟粘贴助记词: ${value.substring(0, 20)}...`, 'info');

        // 确保页面有焦点
        window.focus();
        document.body.focus();
        await wait(100);

        // 清空并聚焦到输入框
        element.value = '';
        element.focus();
        element.click(); // 额外点击确保焦点
        log('已聚焦到第一个输入框', 'info');

        await wait(300);

        // 尝试写入剪贴板
        try {
            await navigator.clipboard.writeText(value);
            log('助记词已写入剪贴板', 'success');

            // 创建更真实的粘贴事件
            const pasteEvent = new ClipboardEvent('paste', {
                bubbles: true,
                cancelable: true,
                clipboardData: new DataTransfer()
            });

            // 设置剪贴板数据
            Object.defineProperty(pasteEvent, 'clipboardData', {
                value: {
                    getData: (format) => {
                        if (format === 'text/plain' || format === 'text') {
                            return value;
                        }
                        return '';
                    },
                    types: ['text/plain'],
                    items: [{
                        kind: 'string',
                        type: 'text/plain',
                        getAsString: (callback) => callback(value)
                    }]
                },
                writable: false
            });

            // 触发粘贴事件
            element.dispatchEvent(pasteEvent);
            log('粘贴事件已触发', 'success');

        } catch (clipboardError) {
            log(`剪贴板操作失败: ${clipboardError.message}`, 'warning');
            throw clipboardError;
        }

    } catch (error) {
        log(`粘贴模拟失败，使用直接输入方案: ${error.message}`, 'warning');

        // 备选方案：直接设置值
        element.value = value;

        // 触发多种事件确保OKX能检测到变化
        const events = ['input', 'change', 'keyup', 'blur', 'focus'];
        for (const eventType of events) {
            element.dispatchEvent(new Event(eventType, { bubbles: true }));
            await wait(50);
        }

        log('已使用直接输入方案', 'info');
    }
};

// 导入单个助记词
const importMnemonic = async (mnemonic, index) => {
    try {
        log(`开始导入第 ${index + 1}/${mnemonics.length} 个助记词`, 'info');
        
        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(2000);
        
        // 等待页面加载
        const firstInput = await waitForElement('.mnemonic-words-inputs__container__input');
        log('页面加载完成', 'success');
        
        // 清空所有输入框
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
        });
        await wait(500);
        
        // 使用按键模拟粘贴助记词到第一个框
        await simulatePaste(firstInput, mnemonic);
        await wait(1500); // 增加等待时间让自动分割完成
        
        // 验证自动分割
        log('开始验证助记词自动分割结果...', 'info');
        const words = mnemonic.split(' ');
        log(`期望的助记词: ${words.join(', ')}`, 'info');

        // 重新获取输入框以确保是最新状态
        const updatedInputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        log(`找到 ${updatedInputs.length} 个输入框`, 'info');

        // 显示当前输入框的值
        const currentValues = [];
        for (let i = 0; i < updatedInputs.length; i++) {
            const value = updatedInputs[i].value.trim();
            currentValues.push(value || '(空)');
        }
        log(`当前输入框值: ${currentValues.join(', ')}`, 'info');

        // 检查分割结果
        let success = true;
        for (let i = 0; i < Math.min(updatedInputs.length, words.length); i++) {
            const inputValue = updatedInputs[i].value.trim();
            const expectedWord = words[i];
            if (inputValue !== expectedWord) {
                success = false;
                log(`第 ${i + 1} 个输入框不匹配: 期望 "${expectedWord}", 实际 "${inputValue}"`, 'error');
                break;
            }
        }

        if (!success) {
            // 尝试手动分割作为备选方案
            log('自动分割失败，尝试手动分割...', 'warning');

            for (let i = 0; i < Math.min(updatedInputs.length, words.length); i++) {
                const input = updatedInputs[i];

                // 清空输入框
                input.value = '';
                input.focus();
                await wait(50);

                // 设置值
                input.value = words[i];

                // 触发多种事件确保OKX检测到变化
                const events = ['input', 'change', 'keyup', 'blur'];
                for (const eventType of events) {
                    input.dispatchEvent(new Event(eventType, { bubbles: true }));
                    await wait(30);
                }

                log(`已设置第 ${i + 1} 个单词: ${words[i]}`, 'info');
            }

            // 额外等待时间让OKX处理
            await wait(1000);

            // 再次验证
            const finalInputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
            let manualSuccess = true;
            for (let i = 0; i < Math.min(finalInputs.length, words.length); i++) {
                if (finalInputs[i].value.trim() !== words[i]) {
                    manualSuccess = false;
                    break;
                }
            }

            if (!manualSuccess) {
                throw new Error('助记词自动分割和手动分割都失败');
            }
            log('手动分割成功', 'success');

            // 触发最后一个输入框的事件，确保OKX验证完成
            const lastInput = finalInputs[words.length - 1];
            if (lastInput) {
                lastInput.focus();
                lastInput.blur();
                await wait(500);
            }
        } else {
            log('助记词自动分割成功', 'success');
        }
        
        // 等待并检查确认按钮状态
        log('检查确认按钮状态...', 'info');
        const confirmBtn = await waitForElement('#app > div > div._affix_oe51y_42 > div > button');

        // 等待按钮状态更新
        let retryCount = 0;
        const maxRetries = 10;

        while (confirmBtn.disabled && retryCount < maxRetries) {
            log(`确认按钮暂不可用，等待中... (${retryCount + 1}/${maxRetries})`, 'warning');
            await wait(500);

            // 重新触发最后一个输入框的事件
            const lastInput = document.querySelector('.mnemonic-words-inputs__container__input:last-child');
            if (lastInput && lastInput.value.trim()) {
                lastInput.focus();
                lastInput.dispatchEvent(new Event('input', { bubbles: true }));
                lastInput.dispatchEvent(new Event('change', { bubbles: true }));
                lastInput.blur();
            }

            retryCount++;
        }

        if (confirmBtn.disabled) {
            // 尝试点击页面其他地方触发验证
            document.body.click();
            await wait(500);

            if (confirmBtn.disabled) {
                throw new Error('确认按钮仍然不可用，可能助记词验证失败');
            }
        }

        log('确认按钮已可用', 'success');
        confirmBtn.click();
        log('已点击确认按钮', 'success');
        
        // 等待导入完成（检查URL变化）
        const startTime = Date.now();
        while (Date.now() - startTime < 15000) {
            if (!window.location.href.includes('import-with-seed-phrase-and-private-key')) {
                log(`第 ${index + 1} 个助记词导入成功`, 'success');
                return true;
            }
            await wait(500);
        }
        
        throw new Error('导入超时');
        
    } catch (error) {
        log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
        return false;
    }
};

// 批量导入主函数
const batchImport = async () => {
    if (mnemonics.length === 0) {
        log('助记词列表为空，请先添加助记词', 'error');
        return;
    }
    
    log('='.repeat(50), 'info');
    log(`开始批量导入 ${mnemonics.length} 个助记词`, 'info');
    log('='.repeat(50), 'info');
    
    let successCount = 0;
    let failureCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < mnemonics.length; i++) {
        const mnemonic = mnemonics[i].trim();
        if (!mnemonic) continue;
        
        const result = await importMnemonic(mnemonic, i);
        if (result) {
            successCount++;
        } else {
            failureCount++;
        }
        
        // 等待间隔
        if (i < mnemonics.length - 1) {
            log(`等待 ${config.delay}ms 后继续...`, 'info');
            await wait(config.delay);
        }
    }
    
    // 统计结果
    const totalTime = Math.round((Date.now() - startTime) / 1000);
    log('='.repeat(50), 'info');
    log('批量导入完成！', 'success');
    log(`总耗时: ${totalTime} 秒`, 'info');
    log(`成功: ${successCount} 个`, 'success');
    log(`失败: ${failureCount} 个`, 'error');
    log('='.repeat(50), 'info');
};


log('='.repeat(50), 'info');
log('OKX钱包批量导入脚本已启动 (优化版)', 'success');
log('='.repeat(50), 'info');
log('可用命令:', 'info');
log('- batchImport()  开始批量导入', 'info');
log('- testPaste()    测试粘贴功能', 'info');
log('='.repeat(50), 'info');

// 自动开始批量导入
batchImport();
