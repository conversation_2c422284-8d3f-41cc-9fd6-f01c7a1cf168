# 图标文件说明

本扩展需要以下图标文件：

## 必需的图标文件
- `icon16.png` - 16x16像素，用于扩展管理页面
- `icon48.png` - 48x48像素，用于扩展管理页面和通知
- `icon128.png` - 128x128像素，用于Chrome网上应用店

## 图标设计建议
- 使用简洁的设计风格
- 主色调建议使用蓝色系（与OKX品牌色相近）
- 图标应该清晰表达"批量导入"或"钱包"的概念
- 确保在不同尺寸下都清晰可见

## 创建图标的方法

### 方法1：使用在线图标生成器
1. 访问 https://www.favicon-generator.org/
2. 上传一个高质量的源图片（建议512x512像素）
3. 生成不同尺寸的图标
4. 下载并重命名为对应的文件名

### 方法2：使用设计软件
1. 使用Photoshop、GIMP或Figma等设计软件
2. 创建512x512像素的源文件
3. 导出为16x16、48x48、128x128三种尺寸
4. 保存为PNG格式

### 方法3：使用简单的占位图标
如果暂时没有专业图标，可以使用以下代码生成简单的占位图标：

```html
<!-- 创建一个简单的SVG图标 -->
<svg width="128" height="128" xmlns="http://www.w3.org/2000/svg">
  <rect width="128" height="128" fill="#1890ff" rx="20"/>
  <text x="64" y="70" font-family="Arial" font-size="60" fill="white" text-anchor="middle">W</text>
</svg>
```

## 临时解决方案
如果没有图标文件，可以暂时注释掉manifest.json中的icons部分：

```json
// "icons": {
//   "16": "icons/icon16.png",
//   "48": "icons/icon48.png", 
//   "128": "icons/icon128.png"
// }
```

扩展仍然可以正常工作，只是会使用默认的扩展图标。
