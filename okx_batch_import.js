/**
 * OKX钱包批量导入助记词脚本 - 增强版
 * 使用方法：
 * 1. 修改下方 MNEMONIC_LIST 数组，添加您的助记词
 * 2. 在OKX钱包扩展页面的浏览器控制台中粘贴并运行此脚本
 * 3. 脚本将自动批量导入所有助记词
 */

// ==================== 配置区域 ====================
const CONFIG = {
    // 导入间隔时间（毫秒）
    IMPORT_INTERVAL: 3000,
    // 重试次数
    MAX_RETRY: 3,
    // 页面加载超时时间（毫秒）
    PAGE_LOAD_TIMEOUT: 10000,
    // 导入操作超时时间（毫秒）
    IMPORT_TIMEOUT: 15000,
    // OKX扩展ID
    EXTENSION_ID: 'mcohilncbfahbmgdjkbpemcciiolgcge'
};

// ==================== 助记词列表 ====================
// 请在此处添加您的助记词，每行一个，格式如下：
const MNEMONIC_LIST = [
    "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans",
    // 在此添加更多助记词...
    // "word1 word2 word3 word4 word5 word6 word7 word8 word9 word10 word11 word12",
];

// ==================== 全局变量 ====================
let currentIndex = 0;
let successCount = 0;
let failureCount = 0;
let startTime = Date.now();

// ==================== 工具函数 ====================
const utils = {
    // 延迟函数
    delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
    
    // 日志函数
    log: (message, type = 'info') => {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[${timestamp}] [OKX批量导入]`;
        
        switch(type) {
            case 'success':
                console.log(`%c${prefix} ✅ ${message}`, 'color: #4CAF50; font-weight: bold;');
                break;
            case 'error':
                console.log(`%c${prefix} ❌ ${message}`, 'color: #F44336; font-weight: bold;');
                break;
            case 'warning':
                console.log(`%c${prefix} ⚠️ ${message}`, 'color: #FF9800; font-weight: bold;');
                break;
            case 'progress':
                console.log(`%c${prefix} 🔄 ${message}`, 'color: #2196F3; font-weight: bold;');
                break;
            default:
                console.log(`${prefix} ${message}`);
        }
    },
    
    // 等待元素出现
    waitForElement: (selector, timeout = 5000) => {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素未找到: ${selector}`));
                    return;
                }
                
                setTimeout(checkElement, 100);
            };
            
            checkElement();
        });
    },
    
    // 模拟用户输入事件
    simulateInput: (element, value) => {
        // 清空现有值
        element.value = '';
        element.focus();
        
        // 模拟粘贴事件
        const pasteEvent = new ClipboardEvent('paste', {
            clipboardData: new DataTransfer()
        });
        pasteEvent.clipboardData.setData('text/plain', value);
        element.dispatchEvent(pasteEvent);
        
        // 设置值并触发输入事件
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
};

// ==================== 核心功能函数 ====================
const okxImporter = {
    // 跳转到导入页面
    navigateToImportPage: async () => {
        const importUrl = `chrome-extension://${CONFIG.EXTENSION_ID}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        utils.log(`跳转到导入页面: ${importUrl}`, 'progress');
        
        window.location.href = importUrl;
        
        // 等待页面加载
        await utils.delay(2000);
        
        // 等待关键元素出现
        await utils.waitForElement('.mnemonic-words-inputs__container__input', CONFIG.PAGE_LOAD_TIMEOUT);
        utils.log('导入页面加载完成', 'success');
    },
    
    // 清空所有输入框
    clearInputs: async () => {
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        utils.log(`清空 ${inputs.length} 个输入框`, 'progress');
        
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
        });
        
        await utils.delay(500);
    },
    
    // 输入助记词
    inputMnemonic: async (mnemonic) => {
        utils.log(`输入助记词: ${mnemonic.substring(0, 20)}...`, 'progress');
        
        // 等待第一个输入框
        const firstInput = await utils.waitForElement('.mnemonic-words-inputs__container__input');
        
        // 清空所有输入框
        await okxImporter.clearInputs();
        
        // 在第一个输入框中输入完整助记词
        utils.simulateInput(firstInput, mnemonic);
        
        // 等待自动分割完成
        await utils.delay(1000);
        
        // 验证分割结果
        const inputs = document.querySelectorAll('.mnemonic-words-inputs__container__input');
        const words = mnemonic.split(' ');
        
        let allFilled = true;
        for (let i = 0; i < Math.min(inputs.length, words.length); i++) {
            if (inputs[i].value.trim() !== words[i]) {
                allFilled = false;
                break;
            }
        }
        
        if (allFilled) {
            utils.log('助记词自动分割成功', 'success');
        } else {
            throw new Error('助记词自动分割失败');
        }
    },
    
    // 点击确认按钮
    clickConfirm: async () => {
        utils.log('点击确认按钮', 'progress');
        
        const confirmButton = await utils.waitForElement('#app > div > div._affix_oe51y_42 > div > button');
        
        // 检查按钮是否可用
        if (confirmButton.disabled) {
            throw new Error('确认按钮不可用');
        }
        
        confirmButton.click();
        utils.log('已点击确认按钮', 'success');
    },
    
    // 等待导入完成
    waitForImportComplete: async () => {
        utils.log('等待导入完成...', 'progress');
        
        const startTime = Date.now();
        
        while (Date.now() - startTime < CONFIG.IMPORT_TIMEOUT) {
            // 检查是否跳转到了新页面或出现了成功提示
            const currentUrl = window.location.href;
            
            // 如果URL发生变化，说明导入可能成功
            if (!currentUrl.includes('import-with-seed-phrase-and-private-key')) {
                utils.log('检测到页面跳转，导入可能成功', 'success');
                return true;
            }
            
            // 检查是否有错误提示
            const errorElement = document.querySelector('.error, .toast-error, [class*="error"]');
            if (errorElement && errorElement.textContent.trim()) {
                throw new Error(`导入失败: ${errorElement.textContent.trim()}`);
            }
            
            await utils.delay(500);
        }
        
        throw new Error('导入超时');
    },
    
    // 导入单个助记词
    importSingle: async (mnemonic, index, retryCount = 0) => {
        try {
            utils.log(`开始导入第 ${index + 1}/${MNEMONIC_LIST.length} 个助记词`, 'progress');
            
            // 跳转到导入页面
            await okxImporter.navigateToImportPage();
            
            // 输入助记词
            await okxImporter.inputMnemonic(mnemonic);
            
            // 点击确认
            await okxImporter.clickConfirm();
            
            // 等待导入完成
            await okxImporter.waitForImportComplete();
            
            successCount++;
            utils.log(`第 ${index + 1} 个助记词导入成功`, 'success');
            
        } catch (error) {
            utils.log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
            
            if (retryCount < CONFIG.MAX_RETRY) {
                utils.log(`准备重试 (${retryCount + 1}/${CONFIG.MAX_RETRY})`, 'warning');
                await utils.delay(2000);
                return await okxImporter.importSingle(mnemonic, index, retryCount + 1);
            } else {
                failureCount++;
                utils.log(`第 ${index + 1} 个助记词重试次数已用完，跳过`, 'error');
            }
        }
    }
};

// ==================== 主执行函数 ====================
const startBatchImport = async () => {
    utils.log('='.repeat(50), 'info');
    utils.log('开始OKX钱包批量导入助记词', 'progress');
    utils.log(`总计 ${MNEMONIC_LIST.length} 个助记词待导入`, 'info');
    utils.log('='.repeat(50), 'info');
    
    if (MNEMONIC_LIST.length === 0) {
        utils.log('助记词列表为空，请先添加助记词到 MNEMONIC_LIST 数组中', 'error');
        return;
    }
    
    startTime = Date.now();
    
    for (let i = 0; i < MNEMONIC_LIST.length; i++) {
        currentIndex = i;
        const mnemonic = MNEMONIC_LIST[i].trim();
        
        if (!mnemonic) {
            utils.log(`跳过空的助记词 (第 ${i + 1} 个)`, 'warning');
            continue;
        }
        
        await okxImporter.importSingle(mnemonic, i);
        
        // 如果不是最后一个，等待间隔时间
        if (i < MNEMONIC_LIST.length - 1) {
            utils.log(`等待 ${CONFIG.IMPORT_INTERVAL}ms 后继续下一个...`, 'info');
            await utils.delay(CONFIG.IMPORT_INTERVAL);
        }
    }
    
    // 输出最终统计
    const endTime = Date.now();
    const totalTime = Math.round((endTime - startTime) / 1000);
    
    utils.log('='.repeat(50), 'info');
    utils.log('批量导入完成！', 'success');
    utils.log(`总耗时: ${totalTime} 秒`, 'info');
    utils.log(`成功: ${successCount} 个`, 'success');
    utils.log(`失败: ${failureCount} 个`, 'error');
    utils.log(`成功率: ${((successCount / MNEMONIC_LIST.length) * 100).toFixed(1)}%`, 'info');
    utils.log('='.repeat(50), 'info');
};

// ==================== 启动脚本 ====================
utils.log('OKX钱包批量导入脚本已加载', 'success');
utils.log('请确保已修改 MNEMONIC_LIST 数组中的助记词列表', 'warning');
utils.log('运行 startBatchImport() 开始批量导入', 'info');

// ==================== 额外功能函数 ====================
const extraFeatures = {
    // 暂停/恢复功能
    pause: () => {
        window.okxImportPaused = true;
        utils.log('批量导入已暂停', 'warning');
    },

    resume: () => {
        window.okxImportPaused = false;
        utils.log('批量导入已恢复', 'success');
    },

    // 停止导入
    stop: () => {
        window.okxImportStopped = true;
        utils.log('批量导入已停止', 'error');
    },

    // 显示当前进度
    showProgress: () => {
        const total = MNEMONIC_LIST.length;
        const completed = successCount + failureCount;
        const progress = ((completed / total) * 100).toFixed(1);

        utils.log(`当前进度: ${completed}/${total} (${progress}%)`, 'info');
        utils.log(`成功: ${successCount}, 失败: ${failureCount}`, 'info');
    },

    // 导出导入日志
    exportLog: () => {
        const log = {
            timestamp: new Date().toISOString(),
            total: MNEMONIC_LIST.length,
            success: successCount,
            failure: failureCount,
            successRate: ((successCount / MNEMONIC_LIST.length) * 100).toFixed(1) + '%'
        };

        console.log('导入日志:', log);
        return log;
    }
};

// 修改主执行函数以支持暂停/停止
const originalImportSingle = okxImporter.importSingle;
okxImporter.importSingle = async (mnemonic, index, retryCount = 0) => {
    // 检查是否被停止
    if (window.okxImportStopped) {
        utils.log('导入已被用户停止', 'warning');
        return;
    }

    // 检查是否被暂停
    while (window.okxImportPaused) {
        utils.log('导入已暂停，等待恢复...', 'warning');
        await utils.delay(1000);
    }

    return await originalImportSingle.call(okxImporter, mnemonic, index, retryCount);
};

// ==================== 使用说明 ====================
utils.log('='.repeat(60), 'info');
utils.log('OKX钱包批量导入脚本已加载完成！', 'success');
utils.log('='.repeat(60), 'info');
utils.log('📝 使用步骤:', 'info');
utils.log('1. 修改 MNEMONIC_LIST 数组，添加您的助记词', 'info');
utils.log('2. 运行 startBatchImport() 开始批量导入', 'info');
utils.log('', 'info');
utils.log('🎮 控制命令:', 'info');
utils.log('- startBatchImport()     开始批量导入', 'info');
utils.log('- extraFeatures.pause()  暂停导入', 'info');
utils.log('- extraFeatures.resume() 恢复导入', 'info');
utils.log('- extraFeatures.stop()   停止导入', 'info');
utils.log('- extraFeatures.showProgress() 显示进度', 'info');
utils.log('- extraFeatures.exportLog()    导出日志', 'info');
utils.log('='.repeat(60), 'info');

// 自动启动（可选，取消注释下面这行）
// startBatchImport();
