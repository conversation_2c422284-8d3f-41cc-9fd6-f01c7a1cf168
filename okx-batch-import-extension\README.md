# OKX钱包批量导入助手 - 浏览器扩展版

一个专业的浏览器扩展，用于批量导入助记词到OKX钱包，提供友好的用户界面和可靠的自动化功能。

## 功能特点

### ✨ 核心功能
- 🚀 **批量导入** - 一次性导入多个助记词
- 🎯 **智能粘贴** - 使用经过验证的ClipboardEvent方法
- 🔄 **自动重试** - 失败时自动重试，提高成功率
- 📊 **实时进度** - 显示导入进度和成功率统计
- ⚙️ **灵活配置** - 可调整导入间隔和重试次数

### 🛡️ 安全特性
- 🔒 **本地存储** - 所有数据存储在本地，不上传到服务器
- 🎭 **权限最小化** - 只请求必要的浏览器权限
- 🔍 **透明操作** - 所有操作都有详细日志记录

### 💡 用户体验
- 🎨 **现代界面** - 简洁美观的用户界面
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🌈 **状态反馈** - 清晰的成功/失败状态提示
- 🎛️ **一键操作** - 简单的开始/停止控制

## 安装方法

### 方法1：开发者模式安装（推荐）

1. **下载扩展文件**
   ```bash
   # 下载整个 okx-batch-import-extension 文件夹
   ```

2. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `okx-batch-import-extension` 文件夹

5. **确认安装**
   - 扩展图标会出现在浏览器工具栏
   - 点击图标即可使用

### 方法2：打包安装

1. **打包扩展**
   - 在扩展管理页面点击"打包扩展程序"
   - 选择扩展文件夹，生成 `.crx` 文件

2. **安装打包文件**
   - 将 `.crx` 文件拖拽到扩展管理页面
   - 确认安装

## 使用方法

### 1. 准备助记词
```
pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans
word1 word2 word3 word4 word5 word6 word7 word8 word9 word10 word11 word12
another mnemonic phrase with twelve words here for testing purposes only
```

### 2. 配置参数
- **导入间隔**：每次导入之间的等待时间（1-60秒）
- **重试次数**：失败时的重试次数（1-10次）

### 3. 开始导入
1. 打开OKX钱包扩展
2. 点击浏览器工具栏中的扩展图标
3. 在弹窗中粘贴助记词列表
4. 调整配置参数
5. 点击"开始批量导入"

### 4. 监控进度
- 实时查看导入进度条
- 查看成功/失败统计
- 随时停止导入过程

## 技术架构

### 文件结构
```
okx-batch-import-extension/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 弹窗逻辑
├── content.js             # 内容脚本
├── background.js          # 后台服务
├── injected.js            # 页面注入脚本
├── icons/                 # 图标文件
└── README.md              # 说明文档
```

### 核心组件

#### 1. Popup（弹窗界面）
- 用户交互界面
- 配置管理
- 进度显示
- 状态反馈

#### 2. Content Script（内容脚本）
- 注入到OKX页面
- 执行自动化操作
- 与popup通信
- 错误处理和重试

#### 3. Background Service（后台服务）
- 消息转发
- 标签页管理
- 设置存储
- 生命周期管理

#### 4. Injected Script（注入脚本）
- 页面上下文操作
- 剪贴板增强
- 事件监听
- DOM操作

### 技术特点

#### 🎯 智能粘贴算法
```javascript
// 使用经过验证的ClipboardEvent方法
const pasteEvent = new ClipboardEvent('paste', {
    bubbles: true,
    cancelable: true,
    composed: true
});

Object.defineProperty(pasteEvent, 'clipboardData', {
    value: {
        getData: (format) => format === 'text/plain' ? text : '',
        types: ['text/plain']
    }
});
```

#### 🔄 可靠的重试机制
```javascript
async importSingleMnemonic(mnemonic, index) {
    let retryCount = 0;
    while (retryCount < this.config.retryCount) {
        try {
            // 执行导入逻辑
            return true;
        } catch (error) {
            retryCount++;
            if (retryCount < this.config.retryCount) {
                await this.wait(2000); // 重试前等待
            }
        }
    }
    return false;
}
```

#### 📊 实时进度跟踪
```javascript
sendProgress() {
    chrome.runtime.sendMessage({
        target: 'popup',
        action: 'importProgress',
        data: {
            currentIndex: this.currentIndex,
            successCount: this.successCount,
            failureCount: this.failureCount
        }
    });
}
```

## 故障排除

### 常见问题

**Q: 扩展无法加载？**
A: 确保已启用开发者模式，并检查文件路径是否正确。

**Q: 无法连接到OKX页面？**
A: 确保OKX钱包扩展已安装并打开相关页面。

**Q: 导入失败率高？**
A: 尝试增加导入间隔时间和重试次数。

**Q: 助记词格式错误？**
A: 确保每个助记词包含12个单词，单词之间用空格分隔。

### 调试方法

1. **查看控制台日志**
   ```javascript
   // 在OKX页面按F12，查看Console标签
   ```

2. **检查扩展状态**
   ```javascript
   // 在扩展管理页面查看错误信息
   ```

3. **重新加载扩展**
   ```javascript
   // 在扩展管理页面点击刷新按钮
   ```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持批量导入助记词
- 提供用户友好的界面
- 实现智能粘贴和重试机制

## 许可证

本项目仅供学习和个人使用，请遵守相关法律法规。

## 免责声明

使用本扩展的风险由用户自行承担。开发者不对因使用本扩展而导致的任何损失负责。
