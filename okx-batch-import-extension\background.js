// background.js - 后台服务工作者
class OKXBatchImportBackground {
    constructor() {
        this.setupEventListeners();
        console.log('OKX批量导入助手后台服务已启动');
    }
    
    setupEventListeners() {
        // 扩展安装时的处理
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('OKX批量导入助手已安装');
                this.showWelcomeNotification();
            } else if (details.reason === 'update') {
                console.log('OKX批量导入助手已更新');
            }
        });
        
        // 处理来自popup和content script的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持消息通道开放
        });
        
        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url &&
                tab.url.includes('chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge')) {
                // OKX页面加载完成，可以注入脚本
                this.injectContentScript(tabId);
            }
        });

        // 监听扩展图标点击
        chrome.action.onClicked.addListener((tab) => {
            if (tab.url && tab.url.includes('chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge')) {
                this.injectContentScript(tab.id);
            }
        });
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'getTabInfo':
                this.getActiveTabInfo(sendResponse);
                break;
                
            case 'checkOKXPage':
                this.checkIfOKXPage(sendResponse);
                break;
                
            case 'logMessage':
                console.log(`[${message.source}] ${message.message}`);
                sendResponse({ logged: true });
                break;
                
            default:
                // 转发消息到其他组件
                this.forwardMessage(message, sender);
                sendResponse({ forwarded: true });
        }
    }
    
    async getActiveTabInfo(sendResponse) {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];
            
            sendResponse({
                success: true,
                tab: {
                    id: activeTab.id,
                    url: activeTab.url,
                    title: activeTab.title,
                    isOKXPage: activeTab.url.includes('chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge')
                }
            });
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }
    
    async checkIfOKXPage(sendResponse) {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];
            
            const isOKXPage = activeTab.url && 
                activeTab.url.includes('chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge');
            
            sendResponse({
                success: true,
                isOKXPage: isOKXPage,
                url: activeTab.url
            });
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }
    
    async injectContentScript(tabId) {
        try {
            // 检查content script是否已经注入
            const results = await chrome.tabs.sendMessage(tabId, { action: 'ping' })
                .catch(() => null);

            if (!results) {
                // 注入content script
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                });

                // 注入辅助脚本
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['injected.js'],
                    world: 'MAIN'
                });

                console.log(`Content script已注入到标签页 ${tabId}`);
            }
        } catch (error) {
            console.error('注入content script失败:', error);
        }
    }
    
    forwardMessage(message, sender) {
        // 如果消息来自popup，转发给content script
        if (sender.tab === undefined) {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                    chrome.tabs.sendMessage(tabs[0].id, message);
                }
            });
        }
        // 如果消息来自content script，转发给popup
        else {
            chrome.runtime.sendMessage(message);
        }
    }
    
    showWelcomeNotification() {
        // 显示欢迎通知
        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'OKX批量导入助手',
                message: '扩展已安装完成！点击扩展图标开始使用。'
            });
        }
    }
    
    // 存储和获取用户设置
    async saveSettings(settings) {
        try {
            await chrome.storage.local.set(settings);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    async getSettings(keys) {
        try {
            const result = await chrome.storage.local.get(keys);
            return { success: true, data: result };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    // 清理存储的数据
    async clearSettings() {
        try {
            await chrome.storage.local.clear();
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

// 初始化后台服务
new OKXBatchImportBackground();
