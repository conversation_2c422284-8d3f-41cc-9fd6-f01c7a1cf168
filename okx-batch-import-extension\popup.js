// popup.js - 扩展弹窗逻辑
class OKXBatchImportPopup {
    constructor() {
        this.isImporting = false;
        this.currentIndex = 0;
        this.totalCount = 0;
        this.successCount = 0;
        this.failureCount = 0;
        
        this.initializeElements();
        this.bindEvents();
        this.loadSettings();
    }
    
    initializeElements() {
        this.mnemonicListEl = document.getElementById('mnemonicList');
        this.importDelayEl = document.getElementById('importDelay');
        this.startImportEl = document.getElementById('startImport');
        this.stopImportEl = document.getElementById('stopImport');
        this.statusEl = document.getElementById('status');
        this.progressEl = document.getElementById('progress');
        this.progressFillEl = document.getElementById('progressFill');
        this.progressTextEl = document.getElementById('progressText');
    }
    
    bindEvents() {
        this.startImportEl.addEventListener('click', () => this.startImport());
        this.stopImportEl.addEventListener('click', () => this.stopImport());
        
        // 保存设置
        this.importDelayEl.addEventListener('change', () => this.saveSettings());
    }
    
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get({
                importDelay: 3,
                mnemonicList: ''
            });

            this.importDelayEl.value = result.importDelay;
            this.mnemonicListEl.value = result.mnemonicList;
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }
    
    async saveSettings() {
        try {
            await chrome.storage.local.set({
                importDelay: parseInt(this.importDelayEl.value),
                mnemonicList: this.mnemonicListEl.value
            });
        } catch (error) {
            console.error('保存设置失败:', error);
        }
    }
    
    async startImport() {
        const mnemonics = this.mnemonicListEl.value
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
            
        if (mnemonics.length === 0) {
            this.showStatus('请输入至少一个助记词', 'error');
            return;
        }
        
        // 验证助记词格式
        const invalidMnemonics = mnemonics.filter(mnemonic => {
            const words = mnemonic.split(' ').filter(w => w.trim());
            return words.length !== 12;
        });
        
        if (invalidMnemonics.length > 0) {
            this.showStatus(`发现 ${invalidMnemonics.length} 个格式错误的助记词（应为12个单词）`, 'error');
            return;
        }
        
        // 保存当前设置
        await this.saveSettings();
        
        // 检查是否在OKX页面
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        const currentTab = tabs[0];

        if (!currentTab.url.includes('chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge')) {
            this.showStatus('请先打开OKX钱包扩展页面', 'error');
            return;
        }

        // 确保content script已注入
        await this.ensureContentScriptInjected(currentTab.id);
        
        // 开始导入
        this.isImporting = true;
        this.currentIndex = 0;
        this.totalCount = mnemonics.length;
        this.successCount = 0;
        this.failureCount = 0;
        
        this.updateUI();
        this.showStatus('开始批量导入...', 'info');
        this.showProgress();
        
        // 发送消息到content script
        try {
            await chrome.tabs.sendMessage(currentTab.id, {
                action: 'startBatchImport',
                data: {
                    mnemonics: mnemonics,
                    importDelay: parseInt(this.importDelayEl.value) * 1000
                }
            });
        } catch (error) {
            this.showStatus('无法连接到OKX页面，请刷新页面后重试', 'error');
            this.stopImport();
        }
    }
    
    stopImport() {
        this.isImporting = false;
        this.updateUI();
        this.showStatus('导入已停止', 'info');
        
        // 发送停止消息
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, { action: 'stopImport' });
            }
        });
    }
    
    updateUI() {
        this.startImportEl.style.display = this.isImporting ? 'none' : 'block';
        this.stopImportEl.style.display = this.isImporting ? 'block' : 'none';
        this.startImportEl.disabled = this.isImporting;
    }
    
    showStatus(message, type) {
        this.statusEl.textContent = message;
        this.statusEl.className = `status status-${type}`;
        this.statusEl.style.display = 'block';
    }
    
    showProgress() {
        this.progressEl.style.display = 'block';
        this.updateProgress();
    }
    
    updateProgress() {
        const progress = this.totalCount > 0 ? (this.currentIndex / this.totalCount) * 100 : 0;
        this.progressFillEl.style.width = `${progress}%`;
        this.progressTextEl.textContent = `${this.currentIndex} / ${this.totalCount} (成功: ${this.successCount}, 失败: ${this.failureCount})`;
    }
    
    async ensureContentScriptInjected(tabId) {
        try {
            // 尝试ping content script
            await chrome.tabs.sendMessage(tabId, { action: 'ping' });
        } catch (error) {
            // 如果失败，说明需要注入
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                });

                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['injected.js'],
                    world: 'MAIN'
                });

                // 等待脚本初始化
                await this.wait(1000);
            } catch (injectError) {
                throw new Error('无法注入脚本到OKX页面');
            }
        }
    }

    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    handleMessage(message) {
        switch (message.action) {
            case 'importProgress':
                this.currentIndex = message.data.currentIndex;
                this.successCount = message.data.successCount;
                this.failureCount = message.data.failureCount;
                this.updateProgress();
                this.showStatus(`正在导入第 ${this.currentIndex + 1} 个助记词...`, 'info');
                break;

            case 'importComplete':
                this.isImporting = false;
                this.updateUI();
                this.currentIndex = this.totalCount;
                this.successCount = message.data.successCount;
                this.failureCount = message.data.failureCount;
                this.updateProgress();

                const successRate = ((this.successCount / this.totalCount) * 100).toFixed(1);
                this.showStatus(
                    `导入完成！成功: ${this.successCount}, 失败: ${this.failureCount}, 成功率: ${successRate}%`,
                    this.successCount === this.totalCount ? 'success' : 'info'
                );
                break;

            case 'importError':
                this.isImporting = false;
                this.updateUI();
                this.showStatus(`导入出错: ${message.data.error}`, 'error');
                break;
        }
    }
}

// 初始化
const popup = new OKXBatchImportPopup();

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    if (message.target === 'popup') {
        popup.handleMessage(message);
    }
    sendResponse({ received: true });
});
