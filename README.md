# OKX钱包批量导入助记词脚本

## 概述
这是一个用于批量导入助记词到OKX钱包浏览器扩展的JavaScript脚本。支持自动化导入多个助记词，包含错误处理、重试机制和详细的进度日志。

## 文件说明

### 1. okx_batch_import.js (增强版)
功能完整的批量导入脚本，包含：
- ✅ 错误处理和重试机制
- ✅ 可配置的导入间隔时间
- ✅ 详细的进度日志和统计
- ✅ 暂停/恢复/停止功能
- ✅ 导入状态跟踪
- ✅ 自动验证助记词分割

### 2. okx_simple_import.js (简化版)
轻量级版本，适合快速使用：
- ✅ 基础批量导入功能
- ✅ 简单的错误处理
- ✅ 进度显示
- ✅ 更少的代码量

## 使用方法

### 步骤1：准备助记词
将您的助记词按以下格式添加到脚本中：
```javascript
const MNEMONIC_LIST = [
    "pony design hip portion ribbon speed ring piece pretty bamboo unveil jeans",
    "word1 word2 word3 word4 word5 word6 word7 word8 word9 word10 word11 word12",
    // 添加更多助记词...
];
```

### 步骤2：打开OKX钱包扩展
1. 确保已安装OKX钱包浏览器扩展
2. 打开扩展页面
3. 按F12打开开发者工具，切换到Console标签

### 步骤3：运行脚本
1. 复制整个脚本内容
2. 粘贴到浏览器控制台
3. 按Enter执行

### 步骤4：开始批量导入
**增强版：**
```javascript
startBatchImport()
```

**简化版：**
```javascript
batchImport()
```

## 控制命令 (仅增强版)

### 基础控制
- `startBatchImport()` - 开始批量导入
- `extraFeatures.pause()` - 暂停导入
- `extraFeatures.resume()` - 恢复导入
- `extraFeatures.stop()` - 停止导入

### 监控功能
- `extraFeatures.showProgress()` - 显示当前进度
- `extraFeatures.exportLog()` - 导出导入日志

## 配置选项 (增强版)

```javascript
const CONFIG = {
    IMPORT_INTERVAL: 3000,      // 导入间隔时间（毫秒）
    MAX_RETRY: 3,               // 重试次数
    PAGE_LOAD_TIMEOUT: 10000,   // 页面加载超时时间
    IMPORT_TIMEOUT: 15000,      // 导入操作超时时间
    EXTENSION_ID: 'mcohilncbfahbmgdjkbpemcciiolgcge'  // OKX扩展ID
};
```

## 安全注意事项

⚠️ **重要安全提醒：**
1. 仅在您信任的环境中使用此脚本
2. 确保助记词来源可靠且合法
3. 建议在测试环境中先验证脚本功能
4. 导入前请备份现有钱包数据
5. 不要在公共场所或不安全的网络环境中使用

## 故障排除

### 常见问题

**Q: 脚本运行后没有反应？**
A: 检查控制台是否有错误信息，确保OKX扩展已正确安装并启用。

**Q: 助记词分割失败？**
A: 确保助记词格式正确，单词之间用空格分隔，总共12个单词。

**Q: 导入按钮不可用？**
A: 可能是助记词格式不正确或页面未完全加载，脚本会自动重试。

**Q: 如何修改导入间隔时间？**
A: 修改CONFIG.IMPORT_INTERVAL的值（毫秒）。

### 错误代码说明
- `元素未找到` - 页面结构可能发生变化
- `助记词自动分割失败` - 助记词格式不正确
- `确认按钮不可用` - 输入验证失败
- `导入超时` - 网络问题或服务器响应慢

## 技术实现

### 核心功能
1. **页面导航** - 自动跳转到助记词导入页面
2. **输入模拟** - 模拟用户粘贴助记词操作
3. **事件触发** - 触发必要的DOM事件以激活自动分割
4. **状态检测** - 监控导入进程状态
5. **错误恢复** - 自动重试失败的导入操作

### 兼容性
- 支持Chrome、Edge等基于Chromium的浏览器
- 兼容OKX钱包扩展最新版本
- 需要JavaScript ES6+支持

## 更新日志

### v1.0.0
- 初始版本发布
- 支持批量导入助记词
- 包含错误处理和重试机制
- 提供增强版和简化版两个版本

## 许可证
此脚本仅供学习和个人使用，请遵守相关法律法规。

## 免责声明
使用此脚本的风险由用户自行承担。开发者不对因使用此脚本而导致的任何损失负责。
