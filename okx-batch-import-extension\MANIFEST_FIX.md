# Manifest 错误修复说明

## 问题描述
打包扩展时出现错误：
```
Invalid value for 'content_scripts[0].matches[0]': Invalid scheme.
```

## 问题原因
Chrome扩展的manifest.json中不能直接在 `content_scripts` 的 `matches` 字段中使用 `chrome-extension://` 协议。这是Chrome的安全限制。

## 解决方案

### 1. 修改 manifest.json
- **移除** `content_scripts` 配置
- **移除** `host_permissions` 配置  
- **添加** `scripting` 权限
- **修改** `web_accessible_resources` 的 matches 为 `<all_urls>`

### 2. 改为动态注入
使用 `chrome.scripting.executeScript()` API 动态注入脚本，而不是静态配置。

## 具体修改

### manifest.json 修改
```json
// 修改前
{
  "permissions": ["activeTab", "storage", "clipboardWrite", "clipboardRead"],
  "host_permissions": ["chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge/*"],
  "content_scripts": [{
    "matches": ["chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge/*"],
    "js": ["content.js"],
    "run_at": "document_end"
  }]
}

// 修改后
{
  "permissions": ["activeTab", "storage", "clipboardWrite", "clipboardRead", "scripting"],
  "web_accessible_resources": [{
    "resources": ["injected.js"],
    "matches": ["<all_urls>"]
  }]
}
```

### background.js 增强
- 添加标签页更新监听
- 添加扩展图标点击监听
- 实现动态脚本注入逻辑

### popup.js 增强
- 添加脚本注入检查
- 确保content script已加载
- 增强错误处理

### content.js 增强
- 添加ping响应机制
- 改进消息处理逻辑

## 工作原理

1. **检测OKX页面**：background.js监听标签页更新
2. **动态注入**：当检测到OKX页面时，自动注入content.js和injected.js
3. **确保注入**：popup.js在开始导入前确认脚本已注入
4. **通信机制**：使用ping/pong机制检查脚本状态

## 优势

1. **兼容性**：符合Chrome扩展的安全要求
2. **灵活性**：可以根据需要动态注入
3. **可靠性**：确保脚本在需要时才加载
4. **性能**：避免在不需要的页面加载脚本

## 使用方法

修复后的扩展使用方法：

1. **安装扩展**：在Chrome扩展管理页面加载
2. **打开OKX页面**：导航到OKX钱包扩展页面
3. **点击扩展图标**：打开批量导入界面
4. **开始导入**：扩展会自动注入必要的脚本

## 注意事项

- 确保在OKX页面完全加载后再使用扩展
- 如果遇到注入失败，请刷新OKX页面后重试
- 扩展会在每次使用时检查并注入必要的脚本

## 测试验证

修复后可以成功：
- ✅ 打包扩展程序
- ✅ 在Chrome中安装
- ✅ 在OKX页面中正常工作
- ✅ 动态注入content script
- ✅ 正常执行批量导入功能
